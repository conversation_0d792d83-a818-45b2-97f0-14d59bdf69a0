import React, { useState, useEffect, useRef } from 'react';
import { TAROT_CARDS } from '../data/tarot-cards';
import { useTranslation } from 'react-i18next';
import { useUser } from '../contexts/UserContext';
import { useLanguageNavigate } from '../hooks/useLanguageNavigate';
import LandingBackground from '../components/LandingBackground';
import Footer from '../components/Footer';
import axiosInstance from '../utils/axios';
import { useTheme } from '../contexts/ThemeContext';
import SEO from '../components/SEO';
import { isMobileDevice } from '../utils/deviceDetect';
import LoginPrompt from '../components/LoginPrompt';
import { useTarotProgress } from '../hooks/useTarotProgress';
// 导入拆分后的组件
import {
  DailyFortuneCard,
  BirthdayInput,
  StepsSection,
  AnalysisSection,
  FutureSection,
  FAQSection,
  SpotlightSection,
  ActionButton,
  CountdownTimer,
  FortuneReadingModal
} from '../components/daily-fortune';

/**
 * 每日运势页面
 * 
 * 该组件已被重构，将大型组件拆分为多个小组件，以提高可维护性：
 * - DailyFortuneCard: 塔罗牌卡片显示组件
 * - BirthdayInput: 生日输入组件
 * - StepsSection: 三步完成个人化占卜部分
 * - AnalysisSection: 今日运势深度解析部分
 * - FutureSection: 从今日到未来部分
 * - FAQSection: 常见问题部分
 * - SpotlightSection: 底部探索塔罗奥秘部分
 * - ActionButton: 操作按钮组件
 * - CountdownTimer: 倒计时显示组件
 */
const DailyFortune: React.FC = () => {
  const { t, i18n } = useTranslation();
  const { navigate } = useLanguageNavigate();
  const { user } = useUser();
  const [selectedCard, setSelectedCard] = useState<number | null>(null);
  const [cardImage, setCardImage] = useState<string | null>(null);
  const [flipped, setFlipped] = useState(false);
  const [processingCard, setProcessingCard] = useState(false);
  const [cardOrientation, setCardOrientation] = useState(false); // false为正位，true为逆位
  
  // 倒计时相关状态
  const [canPredictToday, setCanPredictToday] = useState(true);
  const [countdown, setCountdown] = useState<string>('');
  const countdownIntervalRef = useRef<number | null>(null);
  const [_isLoading, setIsLoading] = useState(true);
  
  // 卡背图片设置 - 使用固定的绝对路径
  const [cardBackImage] = useState<string>("https://cdn.tarotqa.com/public/home-images-001-sm.webp");
  
  // 添加缓存中的卡牌数据状态
  const [_cachedCardData, setCachedCardData] = useState<any>(null);
  
  // 设备检测
  const [isMobile] = useState<boolean>(() => isMobileDevice());
  const [isIOSDevice] = useState<boolean>(() => {
    const ua = navigator.userAgent.toLowerCase();
    return /iphone|ipad|ipod/.test(ua);
  });
  
  // 登录提示相关状态
  const [showLoginPrompt, setShowLoginPrompt] = useState(false);
  
  // 生日相关状态
  const [showBirthdayInput, setShowBirthdayInput] = useState(false);
  const [birthYear, setBirthYear] = useState<string>('');
  const [birthMonth, setBirthMonth] = useState<string>('');
  const [birthDay, setBirthDay] = useState<string>('');
  const [zodiacSign, setZodiacSign] = useState<string>('');
  const [birthDate, setBirthDate] = useState<string>('');
  
  // 新增运势解读相关状态
  const [showFortuneModal, setShowFortuneModal] = useState(false);
  const [_isGeneratingReading, setIsGeneratingReading] = useState(false); // 前缀下划线表示变量值未直接读取
  const [fortuneResults, setFortuneResults] = useState<any>({});
  const [_initialReadingCompleted, setInitialReadingCompleted] = useState(false);
  
  // 使用useTarotProgress hook来管理进度条
  const {
    progress,
    startProgress,
    markApiReceived,
    completeProgress
  } = useTarotProgress({
    duration: 30000, // 修改为30秒
    onComplete: () => {
      setIsLoading(false);
    }
  });
  
  // 添加获取字体样式的函数
  const getFontClass = () => {
    switch (i18n.language) {
      case 'ja':
        return 'font-sans japanese';
      case 'zh-CN':
      case 'zh-TW':
        return 'font-sans chinese';
      default:
        return 'font-sans';
    }
  };
  
  // 计算星座
  const calculateZodiacSign = () => {
    if (!birthMonth || !birthDay) return;
    
    const month = parseInt(birthMonth);
    const day = parseInt(birthDay);
    
    if (isNaN(month) || isNaN(day)) return;
    
    // 根据月份和日期计算星座
    const zodiacSigns = [
      { name: t('zodiac.aries', '白羊座'), start: { month: 3, day: 21 }, end: { month: 4, day: 19 } },
      { name: t('zodiac.taurus', '金牛座'), start: { month: 4, day: 20 }, end: { month: 5, day: 20 } },
      { name: t('zodiac.gemini', '双子座'), start: { month: 5, day: 21 }, end: { month: 6, day: 21 } },
      { name: t('zodiac.cancer', '巨蟹座'), start: { month: 6, day: 22 }, end: { month: 7, day: 22 } },
      { name: t('zodiac.leo', '狮子座'), start: { month: 7, day: 23 }, end: { month: 8, day: 22 } },
      { name: t('zodiac.virgo', '处女座'), start: { month: 8, day: 23 }, end: { month: 9, day: 22 } },
      { name: t('zodiac.libra', '天秤座'), start: { month: 9, day: 23 }, end: { month: 10, day: 23 } },
      { name: t('zodiac.scorpio', '天蝎座'), start: { month: 10, day: 24 }, end: { month: 11, day: 22 } },
      { name: t('zodiac.sagittarius', '射手座'), start: { month: 11, day: 23 }, end: { month: 12, day: 21 } },
      { name: t('zodiac.capricorn', '摩羯座'), start: { month: 12, day: 22 }, end: { month: 1, day: 19 } },
      { name: t('zodiac.aquarius', '水瓶座'), start: { month: 1, day: 20 }, end: { month: 2, day: 18 } },
      { name: t('zodiac.pisces', '双鱼座'), start: { month: 2, day: 19 }, end: { month: 3, day: 20 } }
    ];
    
    for (const sign of zodiacSigns) {
      if (
        (month === sign.start.month && day >= sign.start.day) || 
        (month === sign.end.month && day <= sign.end.day)
      ) {
        // 特殊处理摩羯座跨年的情况
        if (sign.name === t('zodiac.capricorn', '摩羯座') && month === 1) {
          setZodiacSign(sign.name);
          return;
        } else if (sign.name !== t('zodiac.capricorn', '摩羯座') || month === 12) {
          setZodiacSign(sign.name);
          return;
        }
      }
    }
    
    // 默认为摩羯座（处理跨年的情况）
    if (month === 12 && day >= 22) {
      setZodiacSign(t('zodiac.capricorn', '摩羯座'));
    } else if (month === 1 && day <= 19) {
      setZodiacSign(t('zodiac.capricorn', '摩羯座'));
    }
  };
  
  useTheme(); // 保留钩子调用但不解构变量
  
  // 检查用户今天是否已经进行过每日运势预测
  useEffect(() => {
    checkDailyPredictionStatus();
    
    // 从localStorage加载生日信息
    const savedBirthYear = localStorage.getItem('userBirthYear');
    const savedBirthMonth = localStorage.getItem('userBirthMonth');
    const savedBirthDay = localStorage.getItem('userBirthDay');
    
    if (savedBirthYear) setBirthYear(savedBirthYear);
    if (savedBirthMonth) setBirthMonth(savedBirthMonth);
    if (savedBirthDay) setBirthDay(savedBirthDay);
    
    // 如果有年月日信息，为移动端日期输入框设置初始值
    if (savedBirthYear && savedBirthMonth && savedBirthDay) {
      try {
        // 确保月和日是两位数格式
        const formattedMonth = savedBirthMonth.padStart(2, '0');
        const formattedDay = savedBirthDay.padStart(2, '0');
        const dateString = `${savedBirthYear}-${formattedMonth}-${formattedDay}`;
        setBirthDate(dateString);
      } catch (error) {
      }
    }
    
    // 组件卸载时清除倒计时
    return () => {
      if (countdownIntervalRef.current) {
        window.clearInterval(countdownIntervalRef.current);
      }
    };
  }, [user]);
  
  // 获取今天的日期字符串（YYYY-MM-DD格式）
  const getTodayDateString = () => {
    const today = new Date();
    return `${today.getFullYear()}-${String(today.getMonth() + 1).padStart(2, '0')}-${String(today.getDate()).padStart(2, '0')}`;
  };
  
  // 当生日信息变化时，计算星座
  useEffect(() => {
    calculateZodiacSign();
    
    // 保存生日信息到localStorage
    if (birthYear) localStorage.setItem('userBirthYear', birthYear);
    if (birthMonth) localStorage.setItem('userBirthMonth', birthMonth);
    if (birthDay) localStorage.setItem('userBirthDay', birthDay);
  }, [birthYear, birthMonth, birthDay]);

  // 处理移动端日期输入变化
  useEffect(() => {
    if (birthDate) {
      try {
        const date = new Date(birthDate);
        const year = date.getFullYear().toString();
        // 月份从0开始，所以需要+1
        const month = (date.getMonth() + 1).toString();
        const day = date.getDate().toString();
        
        setBirthYear(year);
        setBirthMonth(month);
        setBirthDay(day);
      } catch (error) {
      }
    }
  }, [birthDate]);

  // 检查用户今日占卜状态
  const checkDailyPredictionStatus = async () => {
    setIsLoading(true);
    
    // 测试模式下，始终允许预测
    if (process.env.REACT_APP_TEST_MODE === 'true') {
      localStorage.removeItem('sessionId');
      setCanPredictToday(true);
      setIsLoading(false);
      return;
    }
    
    // 优先检查本地缓存
    const cachedFortuneStr = localStorage.getItem('dailyFortuneCache');
    if (cachedFortuneStr) {
      try {
        const cachedFortune = JSON.parse(cachedFortuneStr);
        // 检查缓存是否是今天的
        if (cachedFortune.date === getTodayDateString()) {
          // 今天已经进行过预测，设置倒计时
          setCanPredictToday(false);
          startCountdown();
          
          // 如果有卡牌数据，设置卡牌信息
          if (cachedFortune.cardData && cachedFortune.cardData.length > 0) {
            setCachedCardData(cachedFortune.cardData[0]);
            setFlipped(true);
            setCardOrientation(cachedFortune.cardData[0].isReversed || false);
            setCardImage(`/images-optimized/tarot/${TAROT_CARDS[TAROT_CARDS.findIndex(c => c.name === cachedFortune.cardData[0].name)].nameEn}.webp`);
            
            // 查找卡牌索引
            const cardData = cachedFortune.cardData[0];
            const cardInfo = TAROT_CARDS.find(c => c.name === cardData.name || c.nameEn === cardData.nameEn);
            
            if (cardInfo) {
              setSelectedCard(TAROT_CARDS.indexOf(cardInfo));
            } else {
              setSelectedCard(cardData.id || 0);
            }
            
            // 将卡牌信息保存到localStorage
            localStorage.setItem('selectedCards', JSON.stringify([cachedFortune.cardData[0]]));
            
            // 创建一个简单的每日运势牌阵并保存
            const dailySpread = {
              id: "daily-fortune",
              name: "每日运势",
              description: "抽取一张塔罗牌，了解今日运势",
              cardCount: 1,
              positions: ["今日运势"]
            };
            localStorage.setItem('selectedSpread', JSON.stringify(dailySpread));
            
            // 如果有内容，解析运势结果
            if (cachedFortune.content && cachedFortune.content.trim()) {
              setFortuneResults(cachedFortune.content);
              setInitialReadingCompleted(true);
            }
          }
          
          setIsLoading(false);
          return;
        }
      } catch (error) {
        // 如果解析失败，继续检查服务器
      }
    }
    
    // 如果用户未登录，则不进行服务器检查
    if (!user) {
      setCanPredictToday(true);
      setIsLoading(false);
      return;
    }
    
    try {
      // 从服务器获取用户今日占卜状态
      const response = await axiosInstance.get('/api/fortune/daily-status');
      
      if (response.data.dailyLimitReached) {
        // 今天已经进行过预测，设置倒计时
        setCanPredictToday(false);
        
        // 设置下次可用时间
        if (response.data.nextAvailableTime) {
          const nextTime = new Date(response.data.nextAvailableTime);
          startCountdownToTime(nextTime);
        } else {
          startCountdown();
        }
        
        // 从服务器获取用户今日运势卡牌数据
        try {
          const historyResponse = await axiosInstance.get('/api/session/daily-fortune/latest');
          
          if (historyResponse.data.success && historyResponse.data.session) {
            const session = historyResponse.data.session;
            
            // 处理卡牌数据
            if (session.selected_cards || session.cards_json) {
              let selectedCardsData;
              const cardsData = session.selected_cards || session.cards_json;
              
              if (typeof cardsData === 'string') {
                try {
                  selectedCardsData = JSON.parse(cardsData);
                } catch (error) {
                  selectedCardsData = [];
                }
              } else {
                // 已经是对象，直接使用
                selectedCardsData = cardsData;
              }
              
              // 确保卡牌数据格式正确
              if (Array.isArray(selectedCardsData) && selectedCardsData.length > 0) {
                const cardData = selectedCardsData[0];
                
                // 根据卡牌名称找到正确的卡牌信息
                const cardInfo = TAROT_CARDS.find(c => c.name === cardData.name || c.nameEn === cardData.nameEn);
                
                // 设置卡牌信息
                setCachedCardData(cardData);
                setFlipped(true);
                setCardOrientation(cardData.isReversed || false);
                
                // 设置卡牌图片
                setCardImage(`/images-optimized/tarot/${TAROT_CARDS[TAROT_CARDS.findIndex(c => c.name === cardData.name)].nameEn}.webp`);
                
                // 设置选中的卡牌ID - 使用找到的正确卡牌索引
                if (cardInfo) {
                  // @ts-ignore - 忽略类型错误，因为我们知道cardInfo不会是undefined
                  setSelectedCard(TAROT_CARDS.indexOf(cardInfo));
                } else {
                  setSelectedCard(0); // 默认使用愚者牌
                }
                
                // 处理运势解读结果
                if (session.fortune_result) {
                  try {
                    // 解析运势结果
                    let fortuneContent;
                    const fortuneResult = typeof session.fortune_result === 'string'
                      ? JSON.parse(session.fortune_result)
                      : session.fortune_result;
                    
                    // 提取内容
                    if (fortuneResult.reading) {
                      fortuneContent = fortuneResult.reading;
                    } else if (fortuneResult.prologue && fortuneResult.summary) {
                      fortuneContent = `${fortuneResult.prologue}\n\n${fortuneResult.summary}`;
                    } else {
                      fortuneContent = JSON.stringify(fortuneResult);
                    }
                    
                    // 解析运势结果为不同类别
                    setFortuneResults(fortuneResult);
                    setInitialReadingCompleted(true);
                    
                    // 保存到本地缓存
                    const fortuneCache = {
                      date: getTodayDateString(),
                      content: fortuneContent,
                      cardData: [cardData],
                      spread: {
                        id: "daily-fortune",
                        name: "每日运势",
                        description: "抽取一张塔罗牌，了解今日运势",
                        cardCount: 1,
                        positions: ["今日运势"]
                      }
                    };
                    localStorage.setItem('dailyFortuneCache', JSON.stringify(fortuneCache));
                  } catch (error) {
                  }
                }
              }
            }
          }
        } catch (error) {
          // 如果API调用失败，回退到本地存储检查
          fallbackToLocalStorageCheck();
        }
      } else {
        // 今天还没有进行预测
        setCanPredictToday(true);
      }
    } catch (error) {
      
      // 如果API调用失败，回退到本地存储检查
      fallbackToLocalStorageCheck();
    } finally {
      setIsLoading(false);
    }
  };
  
  // 回退到本地存储检查（当API调用失败时）
  const fallbackToLocalStorageCheck = () => {
    // 检查缓存中是否有今天的运势结果
    const cachedFortuneStr = localStorage.getItem('dailyFortuneCache');
    if (!cachedFortuneStr) {
      setCanPredictToday(true);
      return;
    }
    
    try {
      const cachedFortune = JSON.parse(cachedFortuneStr);
      // 检查缓存是否是今天的
      if (cachedFortune.date === getTodayDateString()) {
        // 今天已经进行过预测，设置倒计时
        setCanPredictToday(false);
        startCountdown();
        
        // 如果有卡牌数据，设置卡牌信息
        if (cachedFortune.cardData && cachedFortune.cardData.length > 0) {
          setCachedCardData(cachedFortune.cardData[0]);
          setFlipped(true);
          setCardOrientation(cachedFortune.cardData[0].isReversed || false);
          setCardImage(`/images-optimized/tarot/${TAROT_CARDS[TAROT_CARDS.findIndex(c => c.name === cachedFortune.cardData[0].name)].nameEn}.webp`);
          setSelectedCard(cachedFortune.cardData[0].id || 0);
          
          // 将卡牌信息保存到localStorage
          localStorage.setItem('selectedCards', JSON.stringify([cachedFortune.cardData[0]]));
          
          // 创建一个简单的每日运势牌阵并保存
          const dailySpread = {
            id: "daily-fortune",
            name: "每日运势",
            description: "抽取一张塔罗牌，了解今日运势",
            cardCount: 1,
            positions: ["今日运势"]
          };
          localStorage.setItem('selectedSpread', JSON.stringify(dailySpread));
          
          // 如果有内容，解析运势结果
          if (cachedFortune.content && cachedFortune.content.trim()) {
            setFortuneResults(cachedFortune.content);
            setInitialReadingCompleted(true);
          }
        }
      } else {
        // 不是今天的预测，可以进行新的预测
        setCanPredictToday(true);
      }
    } catch (error) {
      setCanPredictToday(true);
    }
  };

  // 开始倒计时到指定时间
  const startCountdownToTime = (targetTime: Date) => {
    // 清除现有的倒计时
    if (countdownIntervalRef.current) {
      window.clearInterval(countdownIntervalRef.current);
    }
    
    // 更新倒计时函数
    const updateCountdown = () => {
      const now = new Date();
      const diff = targetTime.getTime() - now.getTime();
      
      // 如果已经到了目标时间，重置状态
      if (diff <= 0) {
        setCanPredictToday(true);
        if (countdownIntervalRef.current) {
          window.clearInterval(countdownIntervalRef.current);
          countdownIntervalRef.current = null;
        }
        return;
      }
      
      // 计算小时、分钟和秒
      const hours = Math.floor(diff / (1000 * 60 * 60));
      const minutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60));
      const seconds = Math.floor((diff % (1000 * 60)) / 1000);
      
      // 格式化倒计时字符串
      setCountdown(`${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`);
    };
    
    // 立即更新一次
    updateCountdown();
    
    // 设置定时器，每秒更新一次
    countdownIntervalRef.current = window.setInterval(updateCountdown, 1000);
  };

  // 开始倒计时到明天0点（备用方法）
  const startCountdown = () => {
    // 计算到明天0点的时间差
    const updateCountdown = () => {
      const now = new Date();
      const tomorrow = new Date(now);
      tomorrow.setDate(tomorrow.getDate() + 1);
      tomorrow.setHours(0, 0, 0, 0);
      
      const diff = tomorrow.getTime() - now.getTime();
      
      // 如果已经到了第二天，重置状态
      if (diff <= 0) {
        setCanPredictToday(true);
        if (countdownIntervalRef.current) {
          window.clearInterval(countdownIntervalRef.current);
          countdownIntervalRef.current = null;
        }
        return;
      }
      
      // 计算小时、分钟和秒
      const hours = Math.floor(diff / (1000 * 60 * 60));
      const minutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60));
      const seconds = Math.floor((diff % (1000 * 60)) / 1000);
      
      // 格式化倒计时字符串
      setCountdown(`${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`);
    };
    
    // 立即更新一次
    updateCountdown();
    
    // 设置定时器，每秒更新一次
    if (countdownIntervalRef.current) {
      window.clearInterval(countdownIntervalRef.current);
    }
    countdownIntervalRef.current = window.setInterval(updateCountdown, 1000);
  };

  // 加载卡牌图像
  useEffect(() => {
    const loadCardImage = () => {
      if (selectedCard !== null) {
        const card = TAROT_CARDS[selectedCard];
        const imageName = card.nameEn + '.webp';
        setCardImage(`/images-optimized/tarot/${imageName}`);
      }
    };

    loadCardImage();
  }, [selectedCard]);

  // 检查用户权限
  const checkUserPermission = () => {
    if (!user) {
      // 显示登录提示弹窗，而不是直接跳转
      setShowLoginPrompt(true);
      return false;
    }
    
    // 检查今天是否已经进行过预测
    if (!canPredictToday) {
      return false;
    }
    
    return true;
  };

  const handleCardFlip = async () => {
    if (!canPredictToday || processingCard) {
      return;
    }

    // 清除之前的sessionId，确保使用新的session
    localStorage.removeItem('sessionId');
    
    setProcessingCard(true);
    
    // 检查用户权限
    if (!checkUserPermission()) {
      // 在权限检查失败时重置处理状态
      setProcessingCard(false);
      return;
    }
    
    // 随机选择一张卡牌
    const randomCardIndex = Math.floor(Math.random() * TAROT_CARDS.length);
    setSelectedCard(randomCardIndex);
    
    // 随机确定卡牌正逆位（50%概率）
    const isReversed = Math.random() < 0.5;
    setCardOrientation(isReversed);
    
    // 设置为已翻开状态
    setFlipped(true);
    
    // 设置卡牌图片
    const card = TAROT_CARDS[randomCardIndex];

    setCardImage(`/images-optimized/tarot/${TAROT_CARDS[randomCardIndex].nameEn}.webp`);
    
    // 立即保存卡牌信息到LocalStorage，防止刷新页面后重置
    const cardData = {
      id: randomCardIndex,
      name: card.name,
      nameEn: card.nameEn,
      isReversed: isReversed
    };
    
    // 保存选中的卡牌到本地存储
    localStorage.setItem('selectedCards', JSON.stringify([cardData]));
    
    // 创建一个简单的每日运势牌阵
    const dailySpread = {
      id: "daily-fortune",
      name: "每日运势",
      description: "抽取一张塔罗牌，了解今日运势",
      cardCount: 1,
      positions: ["今日运势"]
    };
    localStorage.setItem('selectedSpread', JSON.stringify(dailySpread));
    
    // 设置问题为"今日运势如何？"
    localStorage.setItem('userQuestion', "今日运势如何？");
    
    // 保存今日运势缓存
    const cachedFortuneData = {
      date: getTodayDateString(),
      cardData: [cardData],
      content: "",
      spread: dailySpread
    };
    localStorage.setItem('dailyFortuneCache', JSON.stringify(cachedFortuneData));
    
    // 如果用户已登录，异步保存到数据库
    if (user) {
      try {
        const sessionId = localStorage.getItem('sessionId');
        if (sessionId) {
          // 使用新的同步接口更新会话数据
          try {
            // 先更新牌阵选择
            await axiosInstance.post('/api/spread-recommendation/select', {
              selectedSpread: dailySpread,
              sessionId: sessionId
            });

            // 再更新卡牌选择
            await axiosInstance.post('/api/reading/cards-selected', {
              selectedCards: [{
                name: card.name,
                nameEn: card.nameEn,
                position: "每日运势",
                isReversed: isReversed
              }],
              sessionId: sessionId
            });
          } catch (error) {
            console.error('保存每日运势数据失败:', error);
          }
        }
      } catch (error) {
      }
    }
    
    // 卡片动画完成后，从处理中状态移除
    setTimeout(() => {
      setProcessingCard(false);
    }, 300);
  };

  const handleViewFortune = async () => {
    if (!flipped || selectedCard === null) {
      return;
    }

    // 检查用户权限
    if (!checkUserPermission()) return;

    // 如果没有卡牌图片但有选中的卡牌，则设置卡牌图片
    if (!cardImage && selectedCard !== null) {
      setCardImage(`/images-optimized/tarot/${TAROT_CARDS[selectedCard].nameEn}.webp`);
    }

    // 准备卡牌数据
    const card = TAROT_CARDS[selectedCard]; // 定义card并在下面使用
    const cardState = {
      cardId: selectedCard,
      isReversed: cardOrientation,
      name: card.name,
      nameEn: card.nameEn,
      position: "每日运势"
    };

    // 设置卡牌数据用于API请求
    const cardsData = [{
      name: card.name,
      isReversed: cardOrientation
    }];

    // 保存选中的卡牌到本地存储，确保使用正确的ID
    localStorage.setItem('selectedCards', JSON.stringify([{
      id: selectedCard,  // 使用selectedCard作为id，确保与TAROT_CARDS中的索引一致
      name: card.name,
      nameEn: card.nameEn,
      isReversed: cardOrientation
    }]));

    // 创建一个简单的每日运势牌阵
    const dailySpread = {
      id: "daily-fortune",
      name: "每日运势",
      description: "抽取一张塔罗牌，了解今日运势",
      cardCount: 1,
      positions: ["今日运势"]
    };
    localStorage.setItem('selectedSpread', JSON.stringify(dailySpread));

    // 设置问题为"今日运势如何？"
    const userQuestion = "今日运势如何？";
    localStorage.setItem('userQuestion', userQuestion);
    
    // 如果有生日和星座信息，保存到会话中
    let userZodiacInfo = null;
    const userInfo = {
      birthYear: birthYear || '',
      birthMonth: birthMonth || '',
      birthDay: birthDay || '',
      zodiacSign: zodiacSign || ''
    };
    
    if (zodiacSign) {
      localStorage.setItem('userZodiacInfo', JSON.stringify(userInfo));
      userZodiacInfo = userInfo;
    }

    // 显示进度条弹窗
    setShowFortuneModal(true);
    startProgress();
    setIsGeneratingReading(true);

    try {
      // 首先创建会话
      const existingSessionId = localStorage.getItem('sessionId');
      let sessionId = '';
      
      if (!existingSessionId) {
        // 创建新会话
        const sessionResponse = await axiosInstance.post('/api/session', {
          question: userQuestion,
          status: 'cards_selected',
          selectedCards: cardsData,
          selectedSpread: dailySpread,
          userZodiacInfo: userZodiacInfo
        });

        if (!sessionResponse.data.success) {
          throw new Error('创建会话失败');
        }

        sessionId = sessionResponse.data.session.id;
        localStorage.setItem('sessionId', sessionId);
      } else {
        // 使用现有会话
        sessionId = existingSessionId;
        
        // 更新现有会话，使用新的同步接口
        try {
          // 先更新牌阵选择
          await axiosInstance.post('/api/spread-recommendation/select', {
            selectedSpread: dailySpread,
            sessionId: sessionId
          });

          // 再更新卡牌选择
          await axiosInstance.post('/api/reading/cards-selected', {
            selectedCards: [{
              id: selectedCard,
              name: card.name,
              nameEn: card.nameEn,
              position: cardState.position,
              isReversed: cardOrientation
            }],
            sessionId: sessionId
          });
        } catch (updateError) {
          console.error('更新现有会话失败:', updateError);
          // 继续执行，不要让更新错误阻止主流程
        }
      }

      // 调用运势预测API
      const fortuneResponse = await axiosInstance.post('/api/fortune', {
        question: userQuestion,
        selectedCards: cardsData,
        selectedSpread: dailySpread,
        sessionId: sessionId,  // 现在已确保sessionId是字符串
        language: i18n.language,
        userZodiacInfo: userZodiacInfo
      });

      // 标记API响应已收到
      markApiReceived();
      
      // 直接使用API返回的原始数据，不做额外处理
      const readingContent = fortuneResponse.data.reading;
      
      // 保存结果到缓存
      const fortuneCache = {
        date: getTodayDateString(),
        content: readingContent,
        cardData: [{
          id: selectedCard,
          name: card.name,
          nameEn: card.nameEn,
          isReversed: cardOrientation
        }],
        spread: dailySpread
      };
      localStorage.setItem('dailyFortuneCache', JSON.stringify(fortuneCache));
      
      // 记录最后一次占卜时间
      localStorage.setItem('lastFortunePredictionTime', new Date().toISOString());
      
      // 在API请求完成后完成进度条，等待1秒后再跳转到结果页面
      completeProgress(() => {
        // 保持showFortuneModal为true，不要在这里关闭弹窗
        // 设置一个标志，表示准备跳转
        localStorage.setItem('readyToNavigate', 'true');
        
                  // 延迟1秒后执行跳转，但不在这里关闭弹窗
          setTimeout(() => {
            // 注意：我们直接导航而不关闭弹窗
            // React Router会在导航时卸载当前组件，自然会清除弹窗
            navigate(`/daily-fortune-result?sessionId=${sessionId}`);
          }, 1000);
      });
    } catch (error) {
      completeProgress(() => {
        // 保持进度条对话框显示状态直到延迟结束
        // 延迟1秒后再显示错误信息
        setTimeout(() => {
          // 显示错误信息，在alert弹窗显示期间，弹窗不会消失
          alert(t('daily.error.reading_failed', '很抱歉，生成解读时出现了问题，请稍后再试。'));
          // 只有在用户关闭alert后才会执行下面的代码
          setShowFortuneModal(false);
          setIsGeneratingReading(false);
        }, 1000);
      });
    }
  };

  // 处理登录提示弹窗关闭
  const handleLoginPromptClose = () => {
    setShowLoginPrompt(false);
    // 重置处理状态，允许再次点击卡牌
    setProcessingCard(false);
  };
  
  // 处理生日输入框的切换显示
  const toggleBirthdayInput = () => {
    setShowBirthdayInput(prev => !prev);
  };

  return (
    <div className="min-h-screen flex flex-col relative text-white antialiased">
      <SEO />
      <LandingBackground />
      
      {/* 登录提示弹窗 */}
      <LoginPrompt isOpen={showLoginPrompt} onClose={handleLoginPromptClose} />
      
      <div className="flex-grow relative z-10">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8 pt-0 sm:pt-1 pb-8">
          <div className="text-center mt-6 sm:mt-8 md:mt-10 mb-2 sm:mb-3">
            <h1 className={`${i18n.language === 'en' ? 'text-3xl sm:text-4xl md:text-5xl' : 'text-4xl sm:text-5xl md:text-6xl'} font-bold`}>
              {i18n.language === 'en' ? (
                <>
                  <div style={{color: "#C66CCD"}} className="text-purple-400">AI Astrology</div>
                  <div className="mt-2 text-white">Free Daily Tarot Reading</div>
                </>
              ) : i18n.language === 'ja' ? (
                <>
                  <div className="text-white">今日の運勢</div>
                  <div className="mt-2">
                    <span style={{color: "#C66CCD"}} className="text-purple-400">AI星座タロット占い</span>
                  </div>
                </>
              ) : i18n.language === 'zh-CN' ? (
                <>
                  <div className="text-white">今日运势</div>
                  <div className="mt-2">
                    <span style={{color: "#C66CCD"}} className="text-purple-400">AI星座塔罗</span>
                    <span className="text-white">占卜</span>
                  </div>
                </>
              ) : (
                <>
                  <div className="text-white">今日運勢</div>
                  <div className="mt-2">
                    <span style={{color: "#C66CCD"}} className="text-purple-400">AI星座塔羅</span>
                    <span className="text-white">占卜</span>
                  </div>
                </>
              )}
            </h1>
            
            {/* 生日输入组件 */}
            <BirthdayInput
              showBirthdayInput={showBirthdayInput}
              toggleBirthdayInput={toggleBirthdayInput}
              birthYear={birthYear}
              birthMonth={birthMonth}
              birthDay={birthDay}
              birthDate={birthDate}
              zodiacSign={zodiacSign}
              setBirthYear={setBirthYear}
              setBirthMonth={setBirthMonth}
              setBirthDay={setBirthDay}
              setBirthDate={setBirthDate}
              setZodiacSign={setZodiacSign}
              isMobile={isMobile}
              isIOSDevice={isIOSDevice}
              getFontClass={getFontClass}
            />
          </div>

          {/* 倒计时组件 */}
          {!canPredictToday && (
            <CountdownTimer
              countdown={countdown}
              getFontClass={getFontClass}
            />
          )}

          {/* 塔罗牌组件 */}
          <DailyFortuneCard
            cardBackImage={cardBackImage}
            flipped={flipped}
            canPredictToday={canPredictToday}
            cardImage={cardImage}
            selectedCard={selectedCard}
            cardOrientation={cardOrientation}
            tarotCards={TAROT_CARDS}
            handleCardFlip={handleCardFlip}
            getFontClass={getFontClass}
          />

          {/* 操作按钮 */}
          <ActionButton
            flipped={flipped}
            canPredictToday={canPredictToday}
            handleAction={!canPredictToday ? () => {
              // 获取存储的sessionId
              const sessionId = localStorage.getItem('sessionId');
              navigate(sessionId ? `/daily-fortune-result?sessionId=${sessionId}` : '/daily-fortune-result');
            } : handleViewFortune}
            getFontClass={getFontClass}
          />
        </div>
      </div>

      {/* 三步完成个人化占卜仪式板块 */}
      <StepsSection />

      {/* 今日運勢深度解析板块 */}
      <AnalysisSection getFontClass={getFontClass} fortuneResults={fortuneResults} />

      {/* 從今日到未來板块 */}
      <FutureSection getFontClass={getFontClass} />

      {/* 今日运势常见问题(FAQ)板块 */}
      <FAQSection />

      {/* Horoscope Schema 标记 */}
      <script 
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify({
            "@context": "https://schema.org",
            "@type": "Horoscope",
            "name": "TarotQA每日運勢占卜",
            "datePublished": new Date().toISOString().split('T')[0],
            "description": "結合塔羅牌與星座的每日運勢分析，提供整體、愛情、事業、財富與健康等多方面的個人化指引。",
            "about": {
              "@type": "Thing",
              "name": "每日運勢占卜"
            }
          })
        }}
      />

      {/* SpotlightCard组件 */}
      <SpotlightSection />

      {/* 运势解读弹窗 */}
      <FortuneReadingModal
        isOpen={showFortuneModal}
        progress={progress}
      />

      <Footer />
    </div>
  );
};

export default DailyFortune; 