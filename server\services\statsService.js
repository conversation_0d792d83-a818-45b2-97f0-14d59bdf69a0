const { getConnection } = require('./database');
const Feedback = require('../models/Feedback');

class StatsService {
  static async getDailyStats() {
    const pool = await getConnection();
    
    // 获取当前时间（服务器已经是北京时间）
    const now = new Date();
    
    // 设置统计的结束时间点（今天早上8点）
    const endDate = new Date(now);
    endDate.setHours(8, 0, 0, 0);
    
    // 如果当前时间还不到早上8点，则使用昨天早上8点作为结束时间
    if (now < endDate) {
      endDate.setDate(endDate.getDate() - 1);
    }
    
    // 设置统计的开始时间点（前一天早上8点）
    const startDate = new Date(endDate);
    startDate.setDate(startDate.getDate() - 1);

    // 获取统计日期（前一天的日期）
    const statsDate = new Date(startDate);
    const statsDateStr = statsDate.toLocaleDateString('zh-CN', { 
      year: 'numeric',
      month: '2-digit',
      day: '2-digit'
    }).replace(/\//g, '-');

    // 格式化日期为 MySQL datetime 格式
    const formatDate = (date) => {
      return date.toISOString().slice(0, 19).replace('T', ' ');
    };
    
    // console.log('统计时间范围:', {
    //   现在时间: now.toLocaleString('zh-CN'),
    //   统计日期: statsDateStr,
    //   开始时间: formatDate(startDate) + ' (' + startDate.toLocaleString('zh-CN') + ')',
    //   结束时间: formatDate(endDate) + ' (' + endDate.toLocaleString('zh-CN') + ')',
    //   说明: '统计 ' + statsDateStr + ' 的数据（' + 
    //     startDate.toLocaleString('zh-CN') + 
    //     ' 到 ' + 
    //     endDate.toLocaleString('zh-CN') + '）'
    // });

    try {
      // 先测试一下时间范围内是否有数据
      const [[testData]] = await pool.query(`
        SELECT 
          (SELECT COUNT(*) FROM users WHERE created_at BETWEEN ? AND ?) as user_count,
          (SELECT COUNT(*) FROM page_views WHERE timestamp BETWEEN ? AND ?) as pv_count,
          (SELECT COUNT(*) FROM sessions WHERE timestamp BETWEEN ? AND ?) as session_count
      `, [formatDate(startDate), formatDate(endDate), formatDate(startDate), formatDate(endDate), formatDate(startDate), formatDate(endDate)]);
      
      // console.log('测试时间范围内的数据量:', testData);

      // 1. 用户统计
      // 总用户数据
      const [[totalStats]] = await pool.query(`
        SELECT 
          COUNT(*) as total_users,
          COUNT(CASE WHEN vip_status = 'none' THEN 1 END) as total_normal_users,
          COUNT(CASE WHEN vip_status != 'none' THEN 1 END) as total_vip_users
        FROM users
        WHERE created_at < ?
      `, [formatDate(endDate)]);
      
      // console.log('总用户统计结果:', totalStats);

      // 新增用户数据
      const [[newStats]] = await pool.query(`
        SELECT 
          COUNT(*) as new_users,
          COUNT(CASE WHEN vip_status = 'none' THEN 1 END) as new_normal_users,
          COUNT(CASE WHEN vip_status != 'none' THEN 1 END) as new_vip_users,
          COUNT(CASE WHEN remaining_reads = 0 THEN 1 END) as zero_remaining_reads_users
        FROM users
        WHERE created_at BETWEEN ? AND ?
      `, [formatDate(startDate), formatDate(endDate)]);
      
      // console.log('新增用户统计结果:', newStats);

      // 新增用户中体验过一次用户数统计
      // 通过IP地址匹配新增用户和匿名占卜记录
      const [[experiencedUsersStats]] = await pool.query(`
        SELECT COUNT(DISTINCT u.id) as experienced_users_count
        FROM users u
        INNER JOIN anonymous_divination_records adr ON u.ip = adr.ip_address
        WHERE u.created_at BETWEEN ? AND ?
          AND adr.created_at <= u.created_at
      `, [formatDate(startDate), formatDate(endDate)]);

      // console.log('新增用户中体验过一次用户数统计结果:', experiencedUsersStats);

      // 2. 访问量统计
      // PV统计（总页面访问量）
      const [[pageViews]] = await pool.query(`
        SELECT COUNT(*) as total_pv
        FROM page_views
        WHERE timestamp BETWEEN ? AND ?
      `, [formatDate(startDate), formatDate(endDate)]);
      
      // console.log('页面访问统计结果:', pageViews);

      // UV统计（独立访客数）
      const [[uniqueVisitors]] = await pool.query(`
        SELECT 
          COUNT(DISTINCT CASE WHEN user_id IS NOT NULL THEN user_id ELSE CONCAT(ip_address, session_id) END) as total_uv,
          COUNT(DISTINCT user_id) as logged_in_uv,
          COUNT(DISTINCT CASE WHEN user_id IS NULL THEN CONCAT(ip_address, session_id) END) as anonymous_uv
        FROM page_views
        WHERE timestamp BETWEEN ? AND ?
      `, [formatDate(startDate), formatDate(endDate)]);
      
      // console.log('独立访客统计结果:', uniqueVisitors);

      // 页面访问分布
      const [pageStats] = await pool.query(`
        SELECT 
          page_path,
          COUNT(*) as views,
          COUNT(DISTINCT CASE WHEN user_id IS NOT NULL THEN user_id ELSE CONCAT(ip_address, session_id) END) as unique_visitors
        FROM page_views
        WHERE timestamp BETWEEN ? AND ?
        GROUP BY page_path
        ORDER BY views DESC
      `, [formatDate(startDate), formatDate(endDate)]);
      
      // console.log('页面分布统计结果:', pageStats);

      // 3. Sessions 统计
      // 新增 sessions 总数和状态分布
      // 获取基础会话统计
      const [[sessionStats]] = await pool.query(`
        SELECT 
          COUNT(*) as total_sessions,
          COUNT(CASE WHEN status = 'pending' THEN 1 END) as pending_sessions,
          COUNT(CASE WHEN status = 'reader_selected' THEN 1 END) as reader_selected_sessions,
          COUNT(CASE WHEN status = 'spread_selected' THEN 1 END) as spread_selected_sessions,
          COUNT(CASE WHEN status = 'cards_selected' THEN 1 END) as cards_selected_sessions,
          COUNT(CASE WHEN status = 'completed' THEN 1 END) as completed_sessions,
          COUNT(CASE WHEN status = 'failed' THEN 1 END) as failed_sessions,
          COUNT(CASE WHEN ethical_status = 'ethical_intervention' OR status = 'ethical_intervention' THEN 1 END) as ethical_intervention_sessions,
          COUNT(CASE WHEN ethical_status = 'ethical_intervention_follow' OR status = 'ethical_intervention_follow' THEN 1 END) as ethical_intervention_follow_sessions,
          COUNT(CASE WHEN ethical_status = 'potential_ethical_issue' OR status = 'potential_ethical_issue' THEN 1 END) as potential_ethical_issue_sessions,
          COUNT(CASE WHEN ethical_status = 'potential_ethical_issue_follow' OR status = 'potential_ethical_issue_follow' THEN 1 END) as potential_ethical_issue_follow_sessions,
          0 as ethical_followup_sessions
        FROM sessions
        WHERE timestamp BETWEEN ? AND ?
      `, [formatDate(startDate), formatDate(endDate)]);
      
      // 单独获取追问安全检测数据，采用多步处理
      const [sessions] = await pool.query(`
        SELECT id, followup_ethical_categories 
        FROM sessions 
        WHERE 
          timestamp BETWEEN ? AND ? 
          AND followup_ethical_categories IS NOT NULL
      `, [formatDate(startDate), formatDate(endDate)]);
      
      // 追问安全检测数量（排除安全问题的）
      let ethicalFollowupCount = 0;
      const safeCategories = ['无安全问题', '無安全問題', 'No Ethical Issue', '倫理的問題なし'];
      
      // 遍历每个会话判断是否包含安全问题
      for (const session of sessions) {
        try {
          // 检查followup_ethical_categories是否已经是对象
          let categories;
          if (typeof session.followup_ethical_categories === 'object' && session.followup_ethical_categories !== null) {
            // 如果已经是对象，直接使用
            categories = session.followup_ethical_categories;
          } else {
            // 尝试将字符串解析为JSON
            categories = JSON.parse(session.followup_ethical_categories);
          }
          
          // 检查是否有安全问题（即category不在safeCategories中）
          const hasEthicalIssue = categories.some(item => 
            item && item.category && !safeCategories.includes(item.category)
          );
          
          if (hasEthicalIssue) {
            ethicalFollowupCount++;
          }
        } catch (e) {
          console.error(`解析followup_ethical_categories失败，会话ID: ${session.id}, 错误: ${e.message}, 值类型: ${typeof session.followup_ethical_categories}, 值: ${JSON.stringify(session.followup_ethical_categories)}`);
        }
      }
      
      // 更新会话统计中的追问安全检测数
      sessionStats.ethical_followup_sessions = ethicalFollowupCount;

      // 获取匿名sessions统计
      const [[anonymousSessionStats]] = await pool.query(`
        SELECT COUNT(*) as anonymous_sessions
        FROM anonymous_divination_records
        WHERE created_at BETWEEN ? AND ?
      `, [formatDate(startDate), formatDate(endDate)]);

      // 获取匿名sessions的tokens统计
      const [[anonymousTokensStats]] = await pool.query(`
        SELECT
          COUNT(*) as total_calls,
          SUM(input_tokens) as total_input_tokens,
          SUM(output_tokens) as total_output_tokens
        FROM anonymous_divination_records
        WHERE created_at BETWEEN ? AND ?
          AND (input_tokens IS NOT NULL OR output_tokens IS NOT NULL)
      `, [formatDate(startDate), formatDate(endDate)]);

      // 计算总sessions数（包括匿名sessions）
      const totalSessionsIncludingAnonymous = sessionStats.total_sessions + anonymousSessionStats.anonymous_sessions;

      // console.log('会话统计结果:', sessionStats);
      // console.log('匿名会话统计结果:', anonymousSessionStats);

      // LLM API 调用统计
      const [llmStats] = await pool.query(`
        SELECT 
          COUNT(*) as total_calls,
          SUM(input_tokens) as total_input_tokens,
          SUM(output_tokens) as total_output_tokens
        FROM sessions
        WHERE 
          timestamp BETWEEN ? AND ?
          AND status = 'completed'  -- 只统计成功完成的调用
          AND (spread_id != 'daily-fortune' OR spread_id IS NULL)  -- 排除日运解读
      `, [formatDate(startDate), formatDate(endDate)]);
      
      // 日运解读 API 调用统计
      const [dailyFortuneStats] = await pool.query(`
        SELECT 
          COUNT(*) as total_calls,
          SUM(input_tokens) as total_input_tokens,
          SUM(output_tokens) as total_output_tokens
        FROM sessions
        WHERE 
          timestamp BETWEEN ? AND ?
          AND status = 'completed'  -- 只统计成功完成的调用
          AND spread_id = 'daily-fortune'  -- 只统计日运解读
      `, [formatDate(startDate), formatDate(endDate)]);
      
      // 安全检测API调用统计
      const [ethicalStats] = await pool.query(`
        SELECT 
          COUNT(*) as total_ethical_calls,
          SUM(Ethical_input_token) as total_ethical_input_tokens,
          SUM(Ethical_output_token) as total_ethical_output_tokens
        FROM sessions
        WHERE 
          timestamp BETWEEN ? AND ?
          AND (Ethical_input_token > 0 OR Ethical_output_token > 0)
      `, [formatDate(startDate), formatDate(endDate)]);
      
      // 牌阵推荐API调用统计
      const [spreadRecommendationStats] = await pool.query(`
        SELECT 
          COUNT(*) as total_recommend_calls,
          SUM(spread_recommendation_input_tokens) as total_recommend_input_tokens,
          SUM(spread_recommendation_output_tokens) as total_recommend_output_tokens
        FROM sessions
        WHERE 
          timestamp BETWEEN ? AND ?
          AND (spread_recommendation_input_tokens > 0 OR spread_recommendation_output_tokens > 0)
      `, [formatDate(startDate), formatDate(endDate)]);
      
      // console.log('LLM API调用统计结果:', llmStats);

      // 4. 用户反馈统计
      // 获取用户反馈统计数据
      const feedbackStats = await this.getFeedbackStats({
        startDate: formatDate(startDate),
        endDate: formatDate(endDate)
      });

      // 获取用户解读反馈统计（从comments表）
      const [[readingFeedbackStats]] = await pool.query(`
        SELECT COUNT(*) as total_count
        FROM comments
        WHERE created_at BETWEEN ? AND ?
      `, [formatDate(startDate), formatDate(endDate)]);

      // 5. 支付订单统计
      // 新增成功支付订单统计 - 区分货币
      const [[orderTotal]] = await pool.query(`
        SELECT 
          COUNT(*) as total_orders,
          SUM(CASE WHEN currency = 'CNY' OR currency IS NULL THEN amount ELSE 0 END) as total_amount_cny,
          SUM(CASE WHEN currency = 'USD' THEN amount ELSE 0 END) as total_amount_usd,
          COUNT(CASE WHEN currency = 'CNY' OR currency IS NULL THEN 1 END) as cny_orders_count,
          COUNT(CASE WHEN currency = 'USD' THEN 1 END) as usd_orders_count
        FROM payment_orders
        WHERE 
          status = 'success' 
          AND (
            (trade_time BETWEEN ? AND ?) OR 
            (trade_time IS NULL AND payment_method = 'paypal' AND updated_at BETWEEN ? AND ?)
          )
      `, [formatDate(startDate), formatDate(endDate), formatDate(startDate), formatDate(endDate)]);
      
      // 单独统计PayPal订单，用于诊断，增加货币区分
      const [[paypalOrderStats]] = await pool.query(`
        SELECT 
          COUNT(*) as total_orders,
          SUM(CASE WHEN currency = 'CNY' OR currency IS NULL THEN amount ELSE 0 END) as total_amount_cny,
          SUM(CASE WHEN currency = 'USD' THEN amount ELSE 0 END) as total_amount_usd,
          COUNT(CASE WHEN trade_time IS NULL THEN 1 END) as null_trade_time_count,
          COUNT(CASE WHEN currency = 'CNY' OR currency IS NULL THEN 1 END) as cny_orders_count,
          COUNT(CASE WHEN currency = 'USD' THEN 1 END) as usd_orders_count
        FROM payment_orders
        WHERE 
          status = 'success' 
          AND payment_method = 'paypal'
          AND (
            (trade_time BETWEEN ? AND ?) OR 
            (trade_time IS NULL AND updated_at BETWEEN ? AND ?)
          )
      `, [formatDate(startDate), formatDate(endDate), formatDate(startDate), formatDate(endDate)]);


      // 按支付渠道统计订单，区分货币
      const [paymentMethodStats] = await pool.query(`
        SELECT 
          payment_method,
          COUNT(*) as orders_count,
          SUM(CASE WHEN currency = 'CNY' OR currency IS NULL THEN amount ELSE 0 END) as total_amount_cny,
          SUM(CASE WHEN currency = 'USD' THEN amount ELSE 0 END) as total_amount_usd,
          COUNT(CASE WHEN currency = 'CNY' OR currency IS NULL THEN 1 END) as cny_orders_count,
          COUNT(CASE WHEN currency = 'USD' THEN 1 END) as usd_orders_count
        FROM payment_orders
        WHERE 
          status = 'success' 
          AND (
            (trade_time BETWEEN ? AND ?) OR 
            (trade_time IS NULL AND payment_method = 'paypal' AND updated_at BETWEEN ? AND ?)
          )
        GROUP BY payment_method
      `, [formatDate(startDate), formatDate(endDate), formatDate(startDate), formatDate(endDate)]);
      


      // 6. TTS语音缓存统计
      // 统计周期内的免费语音和付费语音字符数及成本
      const [[ttsTotal]] = await pool.query(`
        SELECT 
          SUM(character_count) as total_characters,
          SUM(CASE WHEN voice = 'zh-CN-XiaoxiaoNeural' THEN character_count ELSE 0 END) as free_characters,
          SUM(CASE WHEN voice != 'zh-CN-XiaoxiaoNeural' THEN character_count ELSE 0 END) as paid_characters,
          SUM(cost) as total_cost
        FROM tts_cache
        WHERE created_at BETWEEN ? AND ?
      `, [formatDate(startDate), formatDate(endDate)]);
      
      // 按message_id前缀(category)统计字符数
      const [ttsCategoryStats] = await pool.query(`
        SELECT 
          SUBSTRING_INDEX(message_id, '_', 1) as category,
          COUNT(*) as count,
          SUM(character_count) as total_characters,
          SUM(CASE WHEN voice = 'zh-CN-XiaoxiaoNeural' THEN character_count ELSE 0 END) as free_characters,
          SUM(CASE WHEN voice != 'zh-CN-XiaoxiaoNeural' THEN character_count ELSE 0 END) as paid_characters,
          SUM(cost) as total_cost
        FROM tts_cache
        WHERE 
          created_at BETWEEN ? AND ?
          AND message_id IS NOT NULL
        GROUP BY category
        ORDER BY total_characters DESC
      `, [formatDate(startDate), formatDate(endDate)]);

      // 7. 博客解读数据统计
      // 博客解读总体数据
      const [[blogReadingTotal]] = await pool.query(`
        SELECT 
          COUNT(*) as total_readings,
          SUM(input_tokens) as total_input_tokens,
          SUM(output_tokens) as total_output_tokens,
          SUM(input_tokens) / 1000 * 0.0003 + SUM(output_tokens) / 1000 * 0.0006 as total_cost
        FROM blog_readings
        WHERE created_at BETWEEN ? AND ?
      `, [formatDate(startDate), formatDate(endDate)]);
      
      // 按blog_id统计阅读次数
      const [blogReadingStats] = await pool.query(`
        SELECT 
          blog_id,
          COUNT(*) as reading_count,
          SUM(input_tokens) as input_tokens,
          SUM(output_tokens) as output_tokens,
          SUM(input_tokens) / 1000 * 0.0003 + SUM(output_tokens) / 1000 * 0.0006 as cost
        FROM blog_readings
        WHERE created_at BETWEEN ? AND ?
        GROUP BY blog_id
        ORDER BY reading_count DESC
      `, [formatDate(startDate), formatDate(endDate)]);

      // 8. 分享统计
      const [[shareStats]] = await pool.query(`
        SELECT
          COUNT(*) as total_submissions,
          COUNT(CASE WHEN status = 'pending' THEN 1 END) as pending_submissions,
          COUNT(CASE WHEN status = 'approved' THEN 1 END) as approved_submissions,
          COUNT(CASE WHEN status = 'rejected' THEN 1 END) as rejected_submissions,
          COUNT(CASE WHEN reward_granted = 1 THEN 1 END) as rewards_granted
        FROM share_submissions
        WHERE created_at BETWEEN ? AND ?
      `, [formatDate(startDate), formatDate(endDate)]);

      // 按平台分组的分享统计
      const [sharePlatformStats] = await pool.query(`
        SELECT
          platform,
          COUNT(*) as submission_count,
          COUNT(CASE WHEN status = 'approved' THEN 1 END) as approved_count,
          COUNT(CASE WHEN reward_granted = 1 THEN 1 END) as rewards_count
        FROM share_submissions
        WHERE created_at BETWEEN ? AND ?
        GROUP BY platform
        ORDER BY submission_count DESC
      `, [formatDate(startDate), formatDate(endDate)]);

      return {
        date: statsDateStr,
        users: {
          total: {
            total: totalStats.total_users,
            normal: totalStats.total_normal_users,
            vip: totalStats.total_vip_users
          },
          new: {
            total: newStats.new_users,
            normal: newStats.new_normal_users,
            vip: newStats.new_vip_users,
            zeroRemainingReads: newStats.zero_remaining_reads_users || 0,
            experiencedUsers: experiencedUsersStats.experienced_users_count || 0
          }
        },
        traffic: {
          pageViews: {
            total: pageViews.total_pv,
            byPage: pageStats.map(stat => ({
              path: stat.page_path,
              views: stat.views,
              uniqueVisitors: stat.unique_visitors
            }))
          },
          uniqueVisitors: {
            total: uniqueVisitors.total_uv,
            loggedIn: uniqueVisitors.logged_in_uv,
            anonymous: uniqueVisitors.anonymous_uv
          }
        },
        sessions: {
          total: totalSessionsIncludingAnonymous,
          registered: sessionStats.total_sessions,
          anonymous: anonymousSessionStats.anonymous_sessions,
          byStatus: {
            pending: sessionStats.pending_sessions,
            reader_selected: sessionStats.reader_selected_sessions,
            spread_selected: sessionStats.spread_selected_sessions,
            cards_selected: sessionStats.cards_selected_sessions,
            completed: sessionStats.completed_sessions,
            failed: sessionStats.failed_sessions,
            ethical_intervention: sessionStats.ethical_intervention_sessions || 0,
            ethical_intervention_follow: sessionStats.ethical_intervention_follow_sessions || 0,
            potential_ethical_issue: sessionStats.potential_ethical_issue_sessions || 0,
            potential_ethical_issue_follow: sessionStats.potential_ethical_issue_follow_sessions || 0,
            ethical_followup: sessionStats.ethical_followup_sessions || 0
          },
          llmStats: llmStats.length > 0 ? [{
            calls: llmStats[0].total_calls || 0,
            inputTokens: llmStats[0].total_input_tokens || 0,
            outputTokens: llmStats[0].total_output_tokens || 0,
            inputCost: (llmStats[0].total_input_tokens || 0) / 1000 * 0.0008,
            outputCost: (llmStats[0].total_output_tokens || 0) / 1000 * 0.002,
            totalCost: (llmStats[0].total_input_tokens || 0) / 1000 * 0.0008 + 
                      (llmStats[0].total_output_tokens || 0) / 1000 * 0.002
          }] : [],
          dailyFortuneStats: dailyFortuneStats.length > 0 ? [{
            calls: dailyFortuneStats[0].total_calls || 0,
            inputTokens: dailyFortuneStats[0].total_input_tokens || 0,
            outputTokens: dailyFortuneStats[0].total_output_tokens || 0,
            inputCost: (dailyFortuneStats[0].total_input_tokens || 0) / 1000 * 0.0003,
            outputCost: (dailyFortuneStats[0].total_output_tokens || 0) / 1000 * 0.0006,
            totalCost: (dailyFortuneStats[0].total_input_tokens || 0) / 1000 * 0.0003 + 
                      (dailyFortuneStats[0].total_output_tokens || 0) / 1000 * 0.0006
          }] : [],
          ethicalStats: ethicalStats.length > 0 ? [{
            calls: ethicalStats[0].total_ethical_calls || 0,
            inputTokens: ethicalStats[0].total_ethical_input_tokens || 0,
            outputTokens: ethicalStats[0].total_ethical_output_tokens || 0,
            inputCost: (ethicalStats[0].total_ethical_input_tokens || 0) / 1000 * 0.0003,
            outputCost: (ethicalStats[0].total_ethical_output_tokens || 0) / 1000 * 0.0006,
            totalCost: (ethicalStats[0].total_ethical_input_tokens || 0) / 1000 * 0.0003 + 
                      (ethicalStats[0].total_ethical_output_tokens || 0) / 1000 * 0.0006
          }] : [],
          spreadRecommendationStats: spreadRecommendationStats.length > 0 ? [{
            calls: spreadRecommendationStats[0].total_recommend_calls || 0,
            inputTokens: spreadRecommendationStats[0].total_recommend_input_tokens || 0,
            outputTokens: spreadRecommendationStats[0].total_recommend_output_tokens || 0,
            inputCost: (spreadRecommendationStats[0].total_recommend_input_tokens || 0) / 1000 * 0.0003,
            outputCost: (spreadRecommendationStats[0].total_recommend_output_tokens || 0) / 1000 * 0.0006,
            totalCost: (spreadRecommendationStats[0].total_recommend_input_tokens || 0) / 1000 * 0.0003 +
                      (spreadRecommendationStats[0].total_recommend_output_tokens || 0) / 1000 * 0.0006
          }] : [],
          anonymousStats: [{
            calls: anonymousTokensStats.total_calls || 0,
            inputTokens: anonymousTokensStats.total_input_tokens || 0,
            outputTokens: anonymousTokensStats.total_output_tokens || 0,
            inputCost: (anonymousTokensStats.total_input_tokens || 0) / 1000 * 0.0008,
            outputCost: (anonymousTokensStats.total_output_tokens || 0) / 1000 * 0.002,
            totalCost: (anonymousTokensStats.total_input_tokens || 0) / 1000 * 0.0008 +
                      (anonymousTokensStats.total_output_tokens || 0) / 1000 * 0.002
          }]
        },
        // 添加反馈统计
        feedback: feedbackStats,
        // 添加解读反馈统计（基于comments表）
        readingFeedback: {
          total: readingFeedbackStats.total_count || 0
        },
        // 添加支付订单统计，区分人民币和美元
        orders: {
          total: {
            count: orderTotal.total_orders || 0,
            amountCNY: orderTotal.total_amount_cny || 0,
            amountUSD: orderTotal.total_amount_usd || 0,
            countCNY: orderTotal.cny_orders_count || 0,
            countUSD: orderTotal.usd_orders_count || 0
          },
          byPaymentMethod: paymentMethodStats.map(stat => ({
            method: stat.payment_method,
            count: stat.orders_count,
            amountCNY: stat.total_amount_cny || 0,
            amountUSD: stat.total_amount_usd || 0,
            countCNY: stat.cny_orders_count || 0,
            countUSD: stat.usd_orders_count || 0
          }))
        },
        // 添加TTS缓存统计
        ttsCache: {
          total: {
            freeCharacters: ttsTotal.free_characters || 0,
            paidCharacters: ttsTotal.paid_characters || 0,
            totalCharacters: ttsTotal.total_characters || 0,
            totalCost: ttsTotal.total_cost || 0
          },
          byCategory: ttsCategoryStats.map(stat => ({
            category: stat.category,
            count: stat.count,
            freeCharacters: stat.free_characters || 0,
            paidCharacters: stat.paid_characters || 0,
            totalCharacters: stat.total_characters || 0,
            cost: stat.total_cost || 0
          }))
        },
        // 添加博客阅读统计
        blogReadings: {
          total: {
            count: blogReadingTotal.total_readings || 0,
            inputTokens: blogReadingTotal.total_input_tokens || 0,
            outputTokens: blogReadingTotal.total_output_tokens || 0,
            cost: blogReadingTotal.total_cost || 0
          },
          byBlog: blogReadingStats.map(stat => ({
            blogId: stat.blog_id,
            count: stat.reading_count,
            inputTokens: stat.input_tokens || 0,
            outputTokens: stat.output_tokens || 0,
            cost: stat.cost || 0
          }))
        },
        // 添加分享统计
        shares: {
          total: {
            submissions: shareStats.total_submissions || 0,
            pending: shareStats.pending_submissions || 0,
            approved: shareStats.approved_submissions || 0,
            rejected: shareStats.rejected_submissions || 0,
            rewardsGranted: shareStats.rewards_granted || 0
          },
          byPlatform: sharePlatformStats.map(stat => ({
            platform: stat.platform,
            submissions: stat.submission_count,
            approved: stat.approved_count,
            rewards: stat.rewards_count
          }))
        }
      };
    } catch (error) {
      console.error('Error getting daily stats:', error);
      throw error;
    }
  }

  /**
   * 获取反馈统计数据
   * @param {Object} options - 查询选项
   * @returns {Promise<Object>} 统计数据
   */
  static async getFeedbackStats(options = {}) {
    try {
      // 使用Feedback模型获取统计数据
      const stats = await Feedback.getStats(options);
      return stats;
    } catch (error) {
      console.error('Error getting feedback stats:', error);
      return {
        total: 0,
        byType: { problem: 0, suggestion: 0, other: 0 }
      };
    }
  }

  static async updateUserStats() {
    try {
      const stats = await this.getDailyStats();
      return stats;
    } catch (error) {
      console.error('Error updating user stats:', error);
      throw error;
    }
  }
}

module.exports = StatsService; 