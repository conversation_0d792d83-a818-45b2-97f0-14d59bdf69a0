const express = require('express');
const router = express.Router();
const axios = require('axios');
const { authenticateToken } = require('../middleware/auth');
const { User } = require('../models/User');
const { getConnection } = require('../services/database');
const { Session } = require('../models/tarot');
const { TAROT_CARDS } = require('../constants/tarot');
const { asyncHandler } = require('../utils/asyncHandler');

// 创建用于调用 Qwen API 的 axios 实例
const qwenAPI = axios.create({
  baseURL: "https://dashscope.aliyuncs.com/compatible-mode/v1",
  timeout: 500000,
  headers: {
    'Authorization': `Bearer ${process.env.QWEN_API_KEY}`,
    'Content-Type': 'application/json'
  }
});

// 测试模式开关 - 设置为true时禁用日运24小时限制
const TEST_MODE = false;

// 根据生日获取星座
function getZodiacSign(birthdate) {
  if (!birthdate) return '';
  
  try {
    const date = new Date(birthdate);
    const month = date.getMonth() + 1;
    const day = date.getDate();

    if ((month === 3 && day >= 21) || (month === 4 && day <= 19)) return '白羊座';
    if ((month === 4 && day >= 20) || (month === 5 && day <= 20)) return '金牛座';
    if ((month === 5 && day >= 21) || (month === 6 && day <= 21)) return '双子座';
    if ((month === 6 && day >= 22) || (month === 7 && day <= 22)) return '巨蟹座';
    if ((month === 7 && day >= 23) || (month === 8 && day <= 22)) return '狮子座';
    if ((month === 8 && day >= 23) || (month === 9 && day <= 22)) return '处女座';
    if ((month === 9 && day >= 23) || (month === 10 && day <= 23)) return '天秤座';
    if ((month === 10 && day >= 24) || (month === 11 && day <= 22)) return '天蝎座';
    if ((month === 11 && day >= 23) || (month === 12 && day <= 21)) return '射手座';
    if ((month === 12 && day >= 22) || (month === 1 && day <= 19)) return '摩羯座';
    if ((month === 1 && day >= 20) || (month === 2 && day <= 18)) return '水瓶座';
    if ((month === 2 && day >= 19) || (month === 3 && day <= 20)) return '双鱼座';
    
    return '';
  } catch (e) {
    console.error('解析生日出错:', e);
    return '';
  }
}

// 创建方舟 API 客户端实例
const arkAPI = axios.create({
  baseURL: 'https://ark.cn-beijing.volces.com/api/v3',
  timeout: 1800000, // 30分钟超时
  headers: {
    'Authorization': `Bearer ${process.env.ARK_API_KEY}`,
    'Content-Type': 'application/json'
  }
});

// 添加请求拦截器记录请求日志
arkAPI.interceptors.request.use(config => {
  return config;
}, error => {
  console.error(`[API请求错误] ${new Date().toISOString()} -`, error);
  return Promise.reject(error);
});

// 添加响应拦截器记录响应日志
arkAPI.interceptors.response.use(response => {
  return response;
}, error => {
  console.error(`[API响应错误] ${new Date().toISOString()} -`, error.response ? 
    `状态码: ${error.response.status}, 数据: ${JSON.stringify(error.response.data)}` : 
    error.message);
  return Promise.reject(error);
});

// 清理markdown格式
const cleanMarkdownFormat = (text) => {
  return text
    .replace(/^#+\s+/gm, '') // 移除Markdown标题格式
    .replace(/\*\*(.*?)\*\*/g, '$1') // 移除粗体
    .replace(/\*(.*?)\*/g, '$1') // 移除斜体
    .replace(/\[(.*?)\]\((.*?)\)/g, '$1') // 移除链接
    .replace(/```[a-z]*\n([\s\S]*?)```/g, '$1') // 移除代码块
    .replace(/`(.*?)`/g, '$1'); // 移除行内代码
};

// 解析运势内容为7个部分，包含打分
const parseFortuneContentSections = (content) => {
  if (!content) return {};

  const sections = {
    intro: '',    // 开场语
    overall: '',  // 今日能量总览
    love: '',     // 爱情运势
    career: '',   // 事业运势
    wealth: '',   // 财富运势
    health: '',   // 健康运势
    mystery: ''   // 今日神秘指引
  };
  
  // 存储每个部分的评分 (1-5分)
  const ratings = {
    overall: 0,
    love: 0,
    career: 0,
    wealth: 0,
    health: 0
  };

  // 定义各部分标题的正则表达式 - 使用四种语言中的固定段落名称
  const sectionPatterns = {
    intro: [
      /^开场语$/,             // 简体中文
      /^開場語$/,             // 繁体中文
      /^Introduction$/i,      // 英文
      /^はじめに$/            // 日文
    ],
    overall: [
      /^今日能量总览$/,        // 简体中文
      /^今日能量總覽$/,        // 繁体中文
      /^Today's Energy Overview$/i, // 英文
      /^今日のエネルギー概観$/  // 日文
    ],
    love: [
      /^爱情运势$/,           // 简体中文
      /^愛情運勢$/,           // 繁体中文
      /^Love Fortune$/i,      // 英文
      /^恋愛運$/              // 日文
    ],
    career: [
      /^事业运势$/,           // 简体中文
      /^事業運勢$/,           // 繁体中文
      /^Career Fortune$/i,    // 英文
      /^仕事運$/              // 日文
    ],
    wealth: [
      /^财富运势$/,           // 简体中文
      /^財富運勢$/,           // 繁体中文
      /^Wealth Fortune$/i,    // 英文
      /^金運$/                // 日文
    ],
    health: [
      /^健康运势$/,           // 简体中文
      /^健康運勢$/,           // 繁体中文
      /^Health Fortune$/i,    // 英文
      /^健康運$/              // 日文
    ],
    mystery: [
      /^今日神秘指引$/,        // 简体中文
      /^今日神秘指引$/,        // 繁体中文
      /^Today's Mystic Guidance$/i, // 英文
      /^今日の神秘的なガイダンス$/  // 日文
    ]
  };
  
  // 添加可能的数字前缀或格式处理
  // 针对可能有数字前缀的标题格式（如"1. 开场语"）进行处理
  const sectionPatternsWithPrefix = {};
  for (const [section, patterns] of Object.entries(sectionPatterns)) {
    sectionPatternsWithPrefix[section] = patterns.map(pattern => {
      // 将原始模式转换为字符串，并去掉开头的^和结尾的$
      const patternStr = pattern.toString().replace(/^\/\^/, '').replace(/\$\/[a-z]*$/, '');
      // 添加前缀模式支持，同时允许前后有额外的空白和换行
      // (?:^|\n|\r|\s+) 匹配行首或换行或空白
      // (?:$|\n|\r|\s+) 匹配行尾或换行或空白
      return new RegExp(`(?:^|\\n|\\r|\\s+)(?:\\d+[\\.、\\s]*)?\\s*${patternStr}\\s*(?:$|\\n|\\r|\\s+)`, 'i');
    });
  }
  
  // 使用包含前缀的模式
  const effectiveSectionPatterns = sectionPatternsWithPrefix;
  
  // 添加辅助函数来检查一行是否是标题，而不是内容
  const isContentLine = (line) => {
    // 检查是否包含常见的内容开头词汇
    const contentStartPatterns = [
      /^Today's energy code/i,
      /^Today, your energy/i,
      /^Your energy code/i,
      /^Your mystical guidance/i,
      /^The energy code/i,
      /^For today/i,
      /^今日のエネルギーコード/,
      /^今日のエネルギー密码/,
      /^今日的能量密码/
    ];
    
    return contentStartPatterns.some(pattern => pattern.test(line));
  };

  // 解析星级评分的正则表达式 - 适用于各种格式的评分
  const ratingPattern = /[\(（【]?\s*(\d+)[\/\s]5\s*(?:分?[星⭐]?|stars?)[\)）】]?|rating:\s*(\d+)[\/\s]5\s*(?:stars?)?/i;
  
  // 专门用于匹配段落末尾的评分格式，与prompt格式要求一致：(4/5星)
  const endRatingPattern = /[\(（【]\s*(\d+)\s*[\/\\]\s*5\s*(?:分?[星⭐]?|stars?)[\)）】]?\s*$/i;
  
  // 改进行清理函数，处理可能的多余空白和换行
  const cleanLine = (line) => {
    // 移除行首行尾的空白
    line = line.trim();
    // 移除行内多余的空白
    line = line.replace(/\s+/g, ' ');
    return line;
  };

  // 将内容按标题分割成不同部分
  // 首先，找出所有可能的标题行及其位置
  const titlePositions = [];
  const lines = content.split('\n');
  
  for (let i = 0; i < lines.length; i++) {
    let line = lines[i].trim();
    if (!line) continue;
    
    // 清理行，移除多余空白
    line = cleanLine(line);
    
    // 先检查是否是内容行而不是标题行
    if (isContentLine(line)) {
      continue;
    }
    
    // 为了增强匹配能力，考虑当前行和下一行组合的情况
    // 某些情况下标题可能被分成了多行
    let combinedLine = line;
    if (i + 1 < lines.length && lines[i + 1].trim()) {
      combinedLine = line + ' ' + lines[i + 1].trim();
    }
    
    // 检查这行是否是某个部分的标题
    for (const [section, patterns] of Object.entries(effectiveSectionPatterns)) {
      // 先尝试单行匹配
      if (patterns.some(pattern => pattern.test(line))) {
        // 检查标题行中是否包含评分
        if (section !== 'intro' && section !== 'mystery') {
          const match = line.match(ratingPattern);
          if (match && (match[1] || match[2])) {
            const score = parseInt(match[1] || match[2], 10);
            if (score >= 1 && score <= 5) {
              ratings[section] = score;   
            }
          }
        }
        
        titlePositions.push({
          line: i,
          section: section
        });
        break;
      } 
      // 如果单行匹配失败，尝试组合行匹配
      else if (combinedLine !== line && patterns.some(pattern => pattern.test(combinedLine))) {
        // 检查标题行中是否包含评分
        if (section !== 'intro' && section !== 'mystery') {
          const match = combinedLine.match(ratingPattern);
          if (match && (match[1] || match[2])) {
            const score = parseInt(match[1] || match[2], 10);
            if (score >= 1 && score <= 5) {
              ratings[section] = score;   
            }
          }
        }
        
        titlePositions.push({
          line: i,
          section: section,
          combinedLine: true
        });
        i++; // 跳过下一行，因为它已经作为组合行的一部分被处理了
        break;
      }
    }
  }
  
  // 根据标题位置分割内容
  if (titlePositions.length > 0) {
    
    // 按顺序处理每个标题及其内容
    for (let i = 0; i < titlePositions.length; i++) {
      const currentTitle = titlePositions[i];
      const nextTitle = titlePositions[i + 1] || { line: lines.length };
      
      // 计算内容的起始行，考虑组合行情况
      const startLine = currentTitle.line + (currentTitle.combinedLine ? 2 : 1);
      
      // 获取当前部分的内容（从当前标题的下一行到下一个标题的前一行）
      let sectionContent = [];
      for (let j = startLine; j < nextTitle.line; j++) {
        const line = lines[j].trim();
        if (!line) continue;
        
        // 检查是否是评分行
        if (currentTitle.section !== 'intro' && currentTitle.section !== 'mystery') {
          const match = line.match(ratingPattern);
          if (match && (match[1] || match[2]) && line.length < 30) { // 评分行通常较短
            const score = parseInt(match[1] || match[2], 10);
            if (score >= 1 && score <= 5) {
              ratings[currentTitle.section] = score;
              continue; // 跳过评分行
            }
          }
        }
        
        sectionContent.push(line);
      }
      
      // 将内容保存到对应部分
      if (sectionContent.length > 0) {
        sections[currentTitle.section] = sectionContent.join('\n\n');
      }
    }
  } else {
    // 如果没有找到任何标题，尝试使用段落分割方法
    
    // 尝试两种分割方式：先尝试按空行分段，如果段落太少，再按换行符分行
    let paragraphs = content.split(/\n\s*\n/);
    
    // 如果段落太少（小于3），可能是没有使用空行分隔，尝试按换行符分割
    if (paragraphs.length < 3) {
      paragraphs = content.split('\n');
    }
    
    // 当前处理的类别
    let currentSection = 'overall'; // 默认为overall，确保没有找到标题时内容也能被保存
    
    // 处理每个段落
    paragraphs.forEach((paragraph, index) => {
      const trimmedParagraph = paragraph.trim();
      
      // 跳过空段落
      if (!trimmedParagraph) {
        return;
      }
      
      // 检查是否是内容行而不是标题行
      if (isContentLine(trimmedParagraph)) {
        // 如果是内容行，添加到当前部分
        if (sections[currentSection]) {
          sections[currentSection] += '\n\n' + trimmedParagraph;
        } else {
          sections[currentSection] = trimmedParagraph;
        }
        return;
      }
      
      // 检查是否是新的部分标题
      let isNewSection = false;
      
      for (const [section, patterns] of Object.entries(effectiveSectionPatterns)) {
        if (patterns.some(pattern => pattern.test(trimmedParagraph))) {
          currentSection = section;
          isNewSection = true;
          
          // 检查标题行中是否包含评分 (例如: "爱情运势 (4/5星)")
          if (section !== 'intro' && section !== 'mystery') {
            const match = trimmedParagraph.match(ratingPattern);
            if (match && (match[1] || match[2])) {
              const score = parseInt(match[1] || match[2], 10);
              if (score >= 1 && score <= 5) {
                ratings[section] = score;
              }
            }
          }
          
          break;
        }
      }
      
      // 如果是新部分的标题，不添加到结果中
      if (isNewSection) return;
      
      // 检查当前行是否包含评分信息 (例如: "今日评分：4/5星")
      if (currentSection && currentSection !== 'intro' && currentSection !== 'mystery') {
        const match = trimmedParagraph.match(ratingPattern);
        if (match && (match[1] || match[2])) {
          const score = parseInt(match[1] || match[2], 10);
          if (score >= 1 && score <= 5) {
            ratings[currentSection] = score;
            return; // 跳过将评分行添加到内容中
          }
        }
      }
      
      // 将段落添加到当前部分
      if (!sections[currentSection]) {
        sections[currentSection] = trimmedParagraph;
      } else {
        sections[currentSection] += '\n\n' + trimmedParagraph;
      }
    });
  }
  
  // 清理各部分内容，去除前后空白
  Object.keys(sections).forEach(key => {
    // 做基本清理
    sections[key] = sections[key] ? sections[key].trim() : '';
  });
  
  // 如果没有成功解析出任何部分，将整个内容作为整体运势
  if (Object.values(sections).every(section => !section)) {
    sections.overall = content.trim();
  }

  // 后处理：检查并修复可能的解析问题

  // 2. 解析未能识别的评分信息
  const extractRating = (text, sectionKey) => {
    if (!text || !sectionKey || sectionKey === 'intro' || sectionKey === 'mystery') return;
    
    // 多种评分格式的匹配
    const patterns = [
      /(\d+)[\/\s]5\s*(?:分?[星⭐]?|stars?)/i,
      /rating:\s*(\d+)[\/\s]5/i,
      /fortune rating:\s*(\d+)[\/\s]5/i,
      /energy rating:\s*(\d+)[\/\s]5/i
    ];
    
    for (const pattern of patterns) {
      const match = text.match(pattern);
      if (match && match[1]) {
        const score = parseInt(match[1], 10);
        if (score >= 1 && score <= 5 && ratings[sectionKey] === 0) {
          ratings[sectionKey] = score;
          break;
        }
      }
    }
  };
  
  // 从段落末尾提取评分并清理内容
  const extractRatingFromEnd = (text, sectionKey) => {
    if (!text || !sectionKey || sectionKey === 'intro' || sectionKey === 'mystery') return text;
    
    // 检查段落末尾是否包含评分信息 (按照固定格式 (4/5星) )
    const match = text.match(endRatingPattern);
    
    if (match && match[1]) {
      const score = parseInt(match[1], 10);
      if (score >= 1 && score <= 5) {
        // 设置评分
        ratings[sectionKey] = score;
        // 返回去掉评分的文本
        return text.replace(endRatingPattern, '').trim();
      }
    }
    
    return text;
  };
  
  // 为每个部分尝试提取评分 - 先处理段落末尾格式化的评分
  Object.keys(sections).forEach(key => {
    // 先从段落末尾提取评分并清理内容
    if (sections[key]) {
      sections[key] = extractRatingFromEnd(sections[key], key);
    }
  });
  
  // 如果没有从末尾找到评分，再尝试在全文中提取
  Object.keys(sections).forEach(key => {
    extractRating(sections[key], key);
  });


  // 检查解析结果，如果有任何关键部分为空，则尝试使用双换行符分割法
  const isResultIncomplete = !sections.intro || !sections.overall || !sections.love || 
                           !sections.career || !sections.wealth || !sections.health || !sections.mystery;
  
  if (isResultIncomplete) {
    // 使用双换行符分割整个内容
    const doubleNewlineParagraphs = content.split(/\n\n+/);
    
    // 如果分割后的段落数量太少，可能不是使用双换行符分隔的
    if (doubleNewlineParagraphs.length >= 5) {
      let currentIndex = 0;
      
      // 尝试识别各个部分
      for (let i = 0; i < doubleNewlineParagraphs.length; i++) {
        const paragraph = doubleNewlineParagraphs[i].trim();
        if (!paragraph) continue;
        
        // 检查是否是内容行而不是标题行
        if (isContentLine(paragraph)) {
          continue;
        }
        
        // 检查是否是某个部分的标题
        let matchedSection = null;
        
        // 检查是否匹配任何一个部分的标题
        for (const [section, patterns] of Object.entries(effectiveSectionPatterns)) {
          if (patterns.some(pattern => pattern.test(paragraph))) {
            matchedSection = section;
            
            // 检查标题中是否包含评分
            if (section !== 'intro' && section !== 'mystery') {
              const match = paragraph.match(ratingPattern);
              if (match && (match[1] || match[2])) {
                const score = parseInt(match[1] || match[2], 10);
                if (score >= 1 && score <= 5) {
                  ratings[section] = score;
                }
              }
            }
            
            break;
          }
        }
        
        // 如果是标题，保存下一个段落作为内容
        if (matchedSection && i + 1 < doubleNewlineParagraphs.length) {
          const contentParagraph = doubleNewlineParagraphs[i + 1].trim();
          
          // 只有当内容不是另一个标题时才保存
          let isNextParagraphTitle = false;
          for (const patterns of Object.values(effectiveSectionPatterns)) {
            if (patterns.some(pattern => pattern.test(contentParagraph))) {
              isNextParagraphTitle = true;
              break;
            }
          }
          
          if (!isNextParagraphTitle && contentParagraph) {
            // 检查内容段落末尾是否包含评分信息
            if (matchedSection !== 'intro' && matchedSection !== 'mystery') {
              const cleanedContent = extractRatingFromEnd(contentParagraph, matchedSection);
              sections[matchedSection] = cleanedContent;
            } else {
              sections[matchedSection] = contentParagraph;
            }
          }
        } else if (!matchedSection && i > 0) {
          // 如果不是标题且不是第一段，尝试判断这是否是前一个部分的额外内容
          // 找到最近被识别的部分
          const previousSection = (() => {
            for (let j = i - 1; j >= 0; j--) {
              const prevParagraph = doubleNewlineParagraphs[j].trim();
              for (const [section, patterns] of Object.entries(effectiveSectionPatterns)) {
                if (patterns.some(pattern => pattern.test(prevParagraph))) {
                  return section;
                }
              }
            }
            return null;
          })();
          
          if (previousSection) {
            // 检查这个段落是否包含评分信息
            const match = paragraph.match(ratingPattern);
            if (previousSection !== 'intro' && previousSection !== 'mystery' && 
                match && (match[1] || match[2]) && paragraph.length < 30) {
              const score = parseInt(match[1] || match[2], 10);
              if (score >= 1 && score <= 5) {
                ratings[previousSection] = score;
              }
            } else if (sections[previousSection]) {
              // 否则将它添加到前一部分的内容中
              sections[previousSection] += '\n\n' + paragraph;
            }
          }
        }
      }
      
      // 如果第一段没有被识别为任何部分的标题，则它可能是intro
      if (!sections.intro && doubleNewlineParagraphs.length > 0) {
        const firstParagraph = doubleNewlineParagraphs[0].trim();
        // 确保它不是其他部分的标题
        let isTitle = false;
        for (const [section, patterns] of Object.entries(effectiveSectionPatterns)) {
          if (section !== 'intro' && patterns.some(pattern => pattern.test(firstParagraph))) {
            isTitle = true;
            break;
          }
        }
        
        if (!isTitle) {
          sections.intro = firstParagraph;
        }
      }
    }
  }

  // 添加函数来检查分段检测是否成功
  const checkParagraphDetectionStatus = () => {
    // 每日运势通常需要intro, overall, love, career, wealth, health, mystery七个部分
    // 但mystery部分有时可能没有，所以不检查mystery
    const requiredSections = ['intro', 'overall', 'love', 'career', 'wealth', 'health', 'mystery'];
    
    // 检查所有必要部分是否都有内容
    let missingOrShortSections = [];
    for (const section of requiredSections) {
      if (!sections[section] || sections[section].length < 5) { // 内容至少应该有5个字符
        missingOrShortSections.push(section);
      }
    }
    
    if (missingOrShortSections.length > 0) {
      return 0; // 0表示检测失败
    } else {
      return 1; // 1表示检测成功
    }
  };

  return {
    sections,
    ratings,
    paragraphDetectionStatus: checkParagraphDetectionStatus()
  };
};

// 获取翻译文本的函数
const getTranslatedText = (key, language) => {
  const defaultLang = 'zh-CN';
  const translations = textTranslations[language] || textTranslations[defaultLang];
  return translations[key];
};

// 检查用户今日占卜状态的路由
router.get('/daily-status', authenticateToken, async (req, res) => {
  try {
    const userId = req.user.userId;
    
    // 如果是测试模式，始终返回未达到限制
    if (TEST_MODE) {
      res.json({
        dailyLimitReached: false,
        nextAvailableTime: null,
        todayUsageCount: 0
      });
      return;
    }
    
    // 获取今天的日期范围
    const today = new Date();
    const todayStart = new Date(today.getFullYear(), today.getMonth(), today.getDate(), 0, 0, 0);
    const todayEnd = new Date(today.getFullYear(), today.getMonth(), today.getDate(), 23, 59, 59);
    
    // 改进：直接查询今天最新的每日运势会话，这样即使有多条记录也只看最新的
    const pool = await getConnection();
    const [existingSessions] = await pool.query(`
      SELECT id, status, fortune_result
      FROM sessions
      WHERE user_id = ?
      AND spread_id = 'daily-fortune'
      AND timestamp BETWEEN ? AND ?
      ORDER BY timestamp DESC
      LIMIT 1
    `, [userId, todayStart, todayEnd]);
    
    // 默认未达到限制
    let dailyLimitReached = false;
    let todayUsageCount = 0;
    
    // 只有当查找到会话且会话状态为completed并且有有效的fortune_result时，才视为达到限制
    if (existingSessions.length > 0) {
      const session = existingSessions[0];
      
      // 判断fortune_result是否有效
      const hasValidFortuneResult = session.fortune_result && 
                                   session.fortune_result !== 'null' && 
                                   session.fortune_result !== 'undefined' && 
                                   session.fortune_result !== '{}' &&
                                   session.fortune_result.length > 10; // 确保内容长度足够
      
      // 只有当会话状态为completed且有有效结果时才算达到限制
      if (session.status === 'completed' && hasValidFortuneResult) {
        dailyLimitReached = true;
        todayUsageCount = 1;
      } else {
        dailyLimitReached = false;
        // 虽然有会话但未完成，仍返回0表示可以进行每日运势
        todayUsageCount = 0;
      }
    }
    
    // 如果已达到限制，计算下次可用时间（明天0点）
    let nextAvailableTime = null;
    if (dailyLimitReached) {
      const tomorrow = new Date(today);
      tomorrow.setDate(tomorrow.getDate() + 1);
      tomorrow.setHours(0, 0, 0, 0);
      nextAvailableTime = tomorrow.toISOString();
    }
    
    res.json({
      dailyLimitReached,
      nextAvailableTime,
      todayUsageCount
    });
  } catch (error) {
    console.error('Error checking daily fortune status:', error);
    res.status(500).json({ error: '检查每日运势状态时出错' });
  }
});

router.post('/', authenticateToken, async (req, res) => {
  try {
    const { question, selectedCards, selectedSpread, sessionId, language, userData, userZodiacInfo } = req.body;
    const userId = req.user.userId;

    if (!question || !selectedCards || !selectedSpread || !sessionId) {
      return res.status(400).json({ error: '缺少必要的参数' });
    }

    // 获取用户信息
    const user = await User.findById(userId);
    
    if (!user) {
      return res.status(404).json({ error: '用户不存在' });
    }

    // 获取会话信息
    const pool = await getConnection();
    
    const [sessions] = await pool.query(`
      SELECT 
        s.*,
        s.selected_cards as cards_json
      FROM sessions s
      WHERE s.id = ? AND s.user_id = ?
    `, [sessionId, userId]);

    if (!sessions || sessions.length === 0) {
      return res.status(404).json({ error: '找不到相关会话' });
    }

    // 生成卡牌描述
    let cardsDescription = '';
    const cardsToProcess = selectedCards;
    
    if (cardsToProcess && cardsToProcess.length > 0) {
      const promptLanguage = language || user.language || 'zh-CN';
      
      // 根据语言选择不同的卡牌描述格式
      let cardLabel, positionLabel, reverseLabel;
      
      switch(promptLanguage) {
        case 'en':
          cardLabel = 'Card';
          positionLabel = 'Position';
          reverseLabel = 'Reversed';
          break;
        case 'ja':
          cardLabel = 'カード';
          positionLabel = '位置';
          reverseLabel = '逆位置';
          break;
        case 'zh-TW':
          cardLabel = '牌';
          positionLabel = '位置';
          reverseLabel = '逆位';
          break;
        case 'zh-CN':
        default:
          cardLabel = '牌';
          positionLabel = '位置';
          reverseLabel = '逆位';
          break;
      }
      
      // 获取翻译后的卡牌名称
      const getTranslatedCardName = (card, language) => {
        try {
          // 标准化language参数
          let normalizedLang = language;
          if (normalizedLang.startsWith('en-')) normalizedLang = 'en';
          if (normalizedLang.startsWith('ja-')) normalizedLang = 'ja';
          if (normalizedLang.startsWith('zh-TW')) normalizedLang = 'zh-TW';
          if (normalizedLang.startsWith('zh-CN')) normalizedLang = 'zh-CN';
          
          // 尝试从i18n文件中获取翻译
          const i18nFile = require(`../i18n/locales/${normalizedLang}.json`);
          
          // 判断是大阿卡纳还是小阿卡纳
          const cardName = card.name;
          
          // 检查是否是大阿卡纳牌
          const majorArcana = TAROT_CARDS.find(c => c.name === cardName && !cardName.includes('牌'));
          if (majorArcana) {
            // 找到大阿卡纳牌在数组中的索引
            const index = TAROT_CARDS.indexOf(majorArcana);
            if (index >= 0 && index < 22) {
              // 使用索引从i18n文件中获取翻译
              return i18nFile.reading.cards.major[index];
            }
          }
          
          // 处理小阿卡纳牌
          // 判断牌的花色
          let suit = '';
          if (cardName.includes('权杖') || cardName.includes('權杖')) {
            suit = 'wands';
          } else if (cardName.includes('圣杯') || cardName.includes('聖杯')) {
            suit = 'cups';
          } else if (cardName.includes('宝剑') || cardName.includes('寶劍')) {
            suit = 'swords';
          } else if (cardName.includes('钱币') || cardName.includes('錢幣') || cardName.includes('星币') || cardName.includes('星幣')) {
            suit = 'pentacles';
          }
          
          // 判断牌的数值
          let value = '';
          if (cardName.includes('王牌') || cardName.includes('A')) {
            value = 'ace';
          } else if (cardName.includes('侍者') || cardName.includes('侍從')) {
            value = 'page';
          } else if (cardName.includes('骑士') || cardName.includes('騎士')) {
            value = 'knight';
          } else if (cardName.includes('皇后')) {
            value = 'queen';
          } else if (cardName.includes('国王') || cardName.includes('國王')) {
            value = 'king';
          } else {
            // 尝试提取数字
            const numbers = ['一', '二', '三', '四', '五', '六', '七', '八', '九', '十'];
            for (let i = 0; i < numbers.length; i++) {
              if (cardName.includes(numbers[i]) || cardName.includes(String(i + 1))) {
                value = String(i + 1);
                break;
              }
            }
          }
          
          // 如果找到了花色和数值，从i18n文件中获取翻译
          if (suit && value && i18nFile.reading.cards[suit] && i18nFile.reading.cards[suit][value]) {
            return i18nFile.reading.cards[suit][value];
          }
          
          // 如果无法获取翻译，返回原始名称
          return cardName;
        } catch (error) {
          console.error('Error translating card name:', error);
          return card.name || '未知卡牌';
        }
      };
      
      // 获取翻译后的位置名称
      const getTranslatedPosition = (position, language) => {
        if (!position) return '';
        
        try {
          // 标准化language参数
          let normalizedLang = language;
          if (normalizedLang.startsWith('en-')) normalizedLang = 'en';
          if (normalizedLang.startsWith('ja-')) normalizedLang = 'ja';
          if (normalizedLang.startsWith('zh-TW')) normalizedLang = 'zh-TW';
          if (normalizedLang.startsWith('zh-CN')) normalizedLang = 'zh-CN';
          
          // 如果是默认语言，直接返回
          if (normalizedLang === 'zh-CN') {
            return position;
          }
          
          // 尝试从i18n文件中获取翻译
          const i18nFile = require(`../i18n/locales/${normalizedLang}.json`);
          
          // 遍历所有牌阵，查找匹配的位置名称
          for (const spreadKey in i18nFile.spreads) {
            const spread = i18nFile.spreads[spreadKey];
            if (spread.positions) {
              for (const posKey in spread.positions) {
                // 检查原始位置名称是否匹配
                const zhCNFile = require('../i18n/locales/zh-CN.json');
                if (zhCNFile.spreads[spreadKey]?.positions?.[posKey] === position) {
                  return spread.positions[posKey];
                }
              }
            }
          }
          
          // 如果没有找到匹配的翻译，返回原始位置名称
          return position;
        } catch (error) {
          console.error('Error translating position:', error);
          return position;
        }
      };
      
      cardsToProcess.forEach((card, index) => {
        // 获取翻译后的卡牌名称
        const translatedCardName = getTranslatedCardName(card, promptLanguage);
        
        // 处理位置信息
        let position = '';
        if (card.position) {
          position = getTranslatedPosition(card.position, promptLanguage);
        } else if (selectedSpread && selectedSpread.positions && selectedSpread.positions[index]) {
          position = getTranslatedPosition(selectedSpread.positions[index], promptLanguage);
        }
        
        // 处理逆位信息
        const isReversed = card.isReversed ? reverseLabel : '';
        
        cardsDescription += `${cardLabel} ${index + 1}: ${translatedCardName} ${isReversed ? `(${isReversed})` : ''}\n`;
        if (position) {
          cardsDescription += `${positionLabel}: ${position}\n`;
        }
        cardsDescription += '\n';
      });
    }

    // 根据牌阵类型选择不同的系统提示
    let systemPrompt = '';
    
    // 检查是否是年运占卜
    const isYearlyFortune = selectedSpread.id === 'yearly-fortune' || selectedSpread.spreadType === 'yearly-fortune';

    if (isYearlyFortune) {
      systemPrompt = `
解读要求：
- 按季度从"事业/学业"、"感情"、"身体情况"、"成就"四个方面解读年度运势，提示重大事件，给出应对建议
- 解读采用"总-分-总"结构，先总述年度运势，再分季度解读，最后再次总结年度运势
- 每季度的解读200-300字，且每个方面分段列示
- 解读需结合五行元素和星座运势，结合水晶佩戴建议
- 语言要直白通俗，使用语气词拉进彼此距离
- 不要使用任何markdown格式
- 不要使用任何特殊格式或标记或表情符号
- 使用自然段落分隔，使用空行来分隔不同部分
`;
    } else {
      // 默认每日运势系统提示
      // 根据语言选择不同的系统提示
      const promptLanguage = language || user.language || 'zh-CN';
      
      // 简体中文版本
      const zhCNPrompt = `
## 角色定位

你是一位资深、有同理心且风格现代的塔罗牌解读师。你的解读总是充满智慧、温暖，并能为提问者带来清晰的方向与正向的能量。你的沟通风格非常亲切、直白且口语化，就像与好朋友聊天一样。

## 核心任务

你的任务是根据用户提供的信息（包含：当前日期、出生日期、星座、抽取的塔罗牌），生成一份详细且个性化的今日运势报告。

## 输出结构与规则

解读必须严格遵循以下六个部分，并完全按照此顺序呈现。每个部分都必须包含指定的标题，并且内容要深入、具体，避免空泛的陈述。

### 固定结构（必须输出段落固定标题，标题前不要带有编号）

**开场语**（简短个人化的问候与引导语，营造亲切氛围）
**今日能量总览**（解读牌义核心、数字与元素，为今日定调。结尾给出1-5星评分，格式为：(4/5星)，不要有额外的评分说明。）
**爱情运势**（结合牌义与情感象征，为单身、恋爱中或关系复杂者提供指引。结尾给出1-5星评分，格式为：(4/5星)，不要有额外的评分说明。）
**事业运势**（结合牌义与职场象征，分析工作挑战、机遇与求职方向。结尾给出1-5星评分，格式为：(4/5星)，不要有额外的评分说明。）
**财富运势**（结合牌义与物质象征，提供财务决策与价值观的洞见。结尾给出1-5星评分，格式为：(4/5星)，不要有额外的评分说明。）
**健康运势**（从牌的能量氛围出发，提供身心灵平衡的实用建议。结尾给出1-5星评分，格式为：(4/5星)，不要有额外的评分说明。）
**今日神秘指引**（选择一个具体的"幸运物/色/概念"，作为今日的能量密码）

## 风格与个性化要求

### 语言风格

- 使用亲切、直白、通俗的现代简体中文
- 多使用"啊"、"哦"、"呢"、"呀"、"啦"等语气词拉近距离
- 语气像朋友间的对话，但保持专业与智慧的深度

### 个性化整合

如果用户提供了星座信息，必须在解读的各个部分中：

- 巧妙将该星座的特质（如处女座的务实、天蝎座的深刻）与塔罗牌的牌义进行结合
- 提供更具针对性的建议
- 分析牌的元素与星座元素的互动关系（和谐、冲突或中性）

## 格式化规范

### 绝对禁止

1. 禁止任何表情符号（Emoji）或特殊符号。
2. 禁止任何Markdown格式，例如 '*、'#'、'-'、'~' 等。
`;

      // 繁体中文版本
      const zhTWPrompt = `
## 角色定位

你是一位資深、有同理心且風格現代的塔羅牌解讀師。你的解讀總是充滿智慧、溫暖，並能為提問者帶來清晰的方向與正向的能量。你的溝通風格非常親切、直白且口語化，就像與好朋友聊天一樣。

## 核心任務

你的任務是根據使用者提供的資訊（包含：當前日期、出生日期、星座、抽取的塔羅牌），生成一份詳細且個人化的今日運勢報告。

## 輸出結構與規則

解讀必須嚴格遵循以下六個部分，並完全按照此順序呈現。每個部分都必須包含指定的標題，並且內容要深入、具體，避免空泛的陳述。

### 固定結構（必須輸出段落固定標題，標題前不要帶有編號）

**開場語**（簡短個人化的問候與引導語，營造親切氛圍）
**今日能量總覽**（解讀牌義核心、數字與元素，為今日定調。結尾給出1-5星評分，格式為：(4/5星)，不要有額外的評分說明。）
**愛情運勢**（結合牌義與情感象徵，為單身、戀愛中或關係複雜者提供指引。結尾給出1-5星評分，格式為：(4/5星)，不要有額外的評分說明。）
**事業運勢**（結合牌義與職場象徵，分析工作挑戰、機遇與求職方向。結尾給出1-5星評分，格式為：(4/5星)，不要有額外的評分說明。）
**財富運勢**（結合牌義與物質象徵，提供財務決策與價值觀的洞見。結尾給出1-5星評分，格式為：(4/5星)，不要有額外的評分說明。）
**健康運勢**（從牌的能量氛圍出發，提供身心靈平衡的實用建議。結尾給出1-5星評分，格式為：(4/5星)，不要有額外的評分說明。）
**今日神秘指引**（選擇一個具體的「幸運物/色/概念」，作為今日的能量密碼）

## 風格與個人化要求

### 語言風格

- 使用親切、直白、通俗的現代台灣正體中文
- 多使用「喔」、「啊」、「啦」、「呢」、「呀」等語氣詞拉近距離
- 語氣像朋友間的對話，但保持專業與智慧的深度
- 語言風格符合臺灣人的習慣

### 個人化整合

如果使用者提供了星座資訊，必須在解讀的各個部分中：

- 巧妙將該星座的特質（如處女座的務實、天蠍座的深刻）與塔羅牌的牌義進行結合
- 提供更具針對性的建議
- 分析牌的元素與星座元素的互動關係（和諧、衝突或中性）

## 格式化規範

### 絕對禁止

1. 絕對禁止任何表情符號（Emoji）或特殊符號。
2. 絕對禁止任何Markdown格式，例如 '*、'#'、'-'、'~' 等。
`;

      // 英文版本
      const enPrompt = `
## Role Definition

You are an experienced, empathetic, and modern tarot card reader. Your interpretations are always filled with wisdom and warmth, bringing clear direction and positive energy to the querent. Your communication style is friendly, straightforward, and conversational, as if chatting with a good friend.

## Core Mission

Your task is to generate a detailed and personalized daily fortune report based on the information provided by the user (including: current date, birth date, zodiac sign, and drawn tarot cards).

## Output Structure and Rules

The interpretation must strictly follow these seven parts, presented in this exact order. Each section must include the specified title, and the content should be in-depth and specific, avoiding vague statements.

### Fixed Structure (Required Section Titles, do not include numbers before titles)

**Introduction** (Brief personalized greeting and introductory words to create a warm atmosphere)
**Today's Energy Overview** (Interpret the core meaning of the card, its number and element, setting the tone for the day. End with a 1-5 star rating, format: (4/5 stars), with no additional rating explanation.)
**Love Fortune** (Combine card meanings with emotional symbolism, providing guidance for singles, those in relationships, or with complicated situations. End with a 1-5 star rating, format: (4/5 stars), with no additional rating explanation.)
**Career Fortune** (Combine card meanings with workplace symbolism, analyzing work challenges, opportunities, and job search directions. End with a 1-5 star rating, format: (4/5 stars), with no additional rating explanation.)
**Wealth Fortune** (Combine card meanings with material symbolism, providing insights into financial decisions and values. End with a 1-5 star rating, format: (4/5 stars), with no additional rating explanation.)
**Health Fortune** (Starting from the card's energy atmosphere, provide practical advice for mind-body-spirit balance. End with a 1-5 star rating, format: (4/5 stars), with no additional rating explanation.)
**Today's Mystic Guidance** (Choose a specific "lucky object/color/concept" as today's energy code)

## Style and Personalization Requirements

### Language Style

- Use friendly, straightforward, and accessible modern English
- Use a conversational tone while maintaining professional depth and wisdom
- Be warm and encouraging in your interpretations
- The language style should suit English-speaking habits

### Personalization Integration

If the user has provided zodiac information, you must incorporate it into each section of the reading:

- Cleverly combine the characteristics of that zodiac sign (e.g., Virgo's practicality, Scorpio's depth) with the tarot card meanings
- Provide more targeted advice
- Analyze the interaction between the card's element and the zodiac element (harmonious, conflicting, or neutral)

## Formatting Guidelines

### Absolute Prohibitions

1. No Emojis or special symbols.
2. No Markdown formatting, such as '*', '#', '-', '~', etc.
`;

      // 日文版本
      const jaPrompt = `
## 役割定義

あなたは経験豊富で、共感力があり、現代的なスタイルのタロットカードリーダーです。あなたの解釈は常に知恵と温かさに満ち、質問者に明確な方向性とポジティブなエネルギーをもたらします。あなたのコミュニケーションスタイルは、まるで親しい友人との会話のように、親しみやすく、率直で、会話的です。

## 核心任務

あなたの任務は、ユーザーから提供された情報（現在の日付、生年月日、星座、引いたタロットカード）に基づいて、詳細かつパーソナライズされた今日の運勢レポートを生成することです。

## 出力構造とルール

解釈は、以下の7つの部分を厳密に守り、この正確な順序で提示する必要があります。各セクションには指定されたタイトルを含め、内容は深く具体的で、曖昧な記述を避けるべきです。

### 固定構造（必須セクションタイトル、タイトルの前に番号を付けないでください）

**はじめに**（簡潔な個人的な挨拶と導入の言葉で温かい雰囲気を作る）
**今日のエネルギー概観**（カードの核心的な意味、数字と元素を解釈し、今日の基調を設定する。最後に1-5星評価を付ける、形式：(4/5星)、追加の評価説明はなし。）
**恋愛運**（カードの意味と感情的なシンボルを組み合わせ、独身者、交際中の人、または複雑な状況にある人へのガイダンスを提供する。最後に1-5星評価を付ける、形式：(4/5星)、追加の評価説明はなし。）
**仕事運**（カードの意味と職場のシンボルを組み合わせ、仕事の課題、機会、就職活動の方向性を分析する。最後に1-5星評価を付ける、形式：(4/5星)、追加の評価説明はなし。）
**金運**（カードの意味と物質的なシンボルを組み合わせ、財務決定と価値観についての洞察を提供する。最後に1-5星評価を付ける、形式：(4/5星)、追加の評価説明はなし。）
**健康運**（カードのエネルギーの雰囲気から始めて、心身のバランスのための実用的なアドバイスを提供する。最後に1-5星評価を付ける、形式：(4/5星)、追加の評価説明はなし。）
**今日の神秘的なガイダンス**（今日のエネルギーコードとして、特定の「ラッキーアイテム/色/概念」を選択する）

## スタイルとパーソナライゼーションの要件

### 言語スタイル

- 親しみやすく、率直で、アクセスしやすい現代日本語を使用
- 「ね」「よ」「わ」などの終助詞を使って親しみやすさを表現
- 専門的な深さと知恵を維持しながら、会話的なトーンを使用
- 解釈において温かく励ましの言葉を使う
- 日本人の習慣に合った言葉遣いを心がけてください

### パーソナライゼーションの統合

ユーザーが星座情報を提供している場合、読みの各セクションにそれを組み込む必要があります：

- その星座の特性（例：乙女座の実用性、蠍座の深さ）とタロットカードの意味を巧みに組み合わせる
- より的を絞ったアドバイスを提供する
- カードの元素と星座の元素の相互作用（調和的、対立的、または中立的）を分析する

## フォーマットガイドライン

### 絶対禁止事項

1. 絵文字や特殊記号の使用禁止。
2. Markdown形式（「*」「#」「-」「~」など）の使用禁止。
`;

      // 根据语言选择对应的prompt
      switch(promptLanguage) {
        case 'en':
          systemPrompt = enPrompt;
          break;
        case 'ja':
          systemPrompt = jaPrompt;
          break;
        case 'zh-TW':
          systemPrompt = zhTWPrompt;
          break;
        case 'zh-CN':
        default:
          systemPrompt = zhCNPrompt;
          break;
      }
    }
    
    // 获取用户信息
    let userInfo = '';
    // 获取当前日期并格式化
    const currentDate = new Date();
    const formattedDate = `${currentDate.getFullYear()}年${currentDate.getMonth() + 1}月${currentDate.getDate()}日`;
    
    if (isYearlyFortune) {
      // 优先使用请求体中的userData，如果没有则尝试从会话中获取
      const userDataToUse = userData || (sessions[0].user_data ? JSON.parse(sessions[0].user_data) : null);
      
      if (userDataToUse) {
        try {
          const zodiacSign = getZodiacSign(userDataToUse.birthdate);
          const promptLanguage = language || user.language || 'zh-CN';
          
          // 根据语言选择不同的标签
          let birthLabel, zodiacLabel, genderLabel, occupationLabel, relationshipLabel, dateLabel;
          
          switch(promptLanguage) {
            case 'en':
              birthLabel = 'Birth Date';
              zodiacLabel = 'Zodiac Sign';
              genderLabel = 'Gender';
              occupationLabel = 'Occupation';
              relationshipLabel = 'Relationship Status';
              dateLabel = 'Current Date';
              break;
            case 'ja':
              birthLabel = '生年月日';
              zodiacLabel = '星座';
              genderLabel = '性別';
              occupationLabel = '職業';
              relationshipLabel = '恋愛状況';
              dateLabel = '現在の日付';
              break;
            case 'zh-TW':
              birthLabel = '出生日期';
              zodiacLabel = '星座';
              genderLabel = '性別';
              occupationLabel = '職業';
              relationshipLabel = '感情狀態';
              dateLabel = '當前日期';
              break;
            case 'zh-CN':
            default:
              birthLabel = '出生日期';
              zodiacLabel = '星座';
              genderLabel = '性别';
              occupationLabel = '职业';
              relationshipLabel = '感情状态';
              dateLabel = '当前日期';
              break;
          }
          
          // 性别翻译
          let genderValue;
          if (userDataToUse.gender === 'male') {
            genderValue = promptLanguage === 'en' ? 'Male' : 
                          promptLanguage === 'ja' ? '男性' : 
                          promptLanguage === 'zh-TW' ? '男' : '男';
          } else if (userDataToUse.gender === 'female') {
            genderValue = promptLanguage === 'en' ? 'Female' : 
                          promptLanguage === 'ja' ? '女性' : 
                          promptLanguage === 'zh-TW' ? '女' : '女';
          } else {
            genderValue = promptLanguage === 'en' ? 'Not Provided' : 
                          promptLanguage === 'ja' ? '未提供' : 
                          promptLanguage === 'zh-TW' ? '未提供' : '未提供';
          }
          
          userInfo = `
${birthLabel}：${userDataToUse.birthdate || (promptLanguage === 'en' ? 'Not Provided' : '未提供')}
${zodiacLabel}：${zodiacSign || (promptLanguage === 'en' ? 'Unknown' : '未知')}
${genderLabel}：${genderValue}
${occupationLabel}：${userDataToUse.occupation || (promptLanguage === 'en' ? 'Not Provided' : '未提供')}
${relationshipLabel}：${userDataToUse.relationshipStatus || (promptLanguage === 'en' ? 'Not Provided' : '未提供')}
${dateLabel}：${formattedDate}
`;
        } catch (e) {
          console.error('处理用户数据失败:', e);
        }
      }
    } else {
      // 为每日运势添加当前日期和星座信息（如果有）
      const promptLanguage = language || user.language || 'zh-CN';
      
      // 根据语言选择不同的标签
      let dateLabel, birthLabel, zodiacLabel;
      
      switch(promptLanguage) {
        case 'en':
          dateLabel = 'Current Date';
          birthLabel = 'Birth Date';
          zodiacLabel = 'Zodiac Sign';
          break;
        case 'ja':
          dateLabel = '現在の日付';
          birthLabel = '生年月日';
          zodiacLabel = '星座';
          break;
        case 'zh-TW':
          dateLabel = '當前日期';
          birthLabel = '出生日期';
          zodiacLabel = '星座';
          break;
        case 'zh-CN':
        default:
          dateLabel = '当前日期';
          birthLabel = '出生日期';
          zodiacLabel = '星座';
          break;
      }
      
      userInfo = `
${dateLabel}：${formattedDate}
`;
      
      // 如果提供了用户星座信息，添加到prompt中
      if (userZodiacInfo) {
        let birthDateStr = '';
        if (userZodiacInfo.birthYear && userZodiacInfo.birthMonth && userZodiacInfo.birthDay) {
          birthDateStr = `${userZodiacInfo.birthYear}年${userZodiacInfo.birthMonth}月${userZodiacInfo.birthDay}日`;
        }
        
        userInfo += birthDateStr ? `${birthLabel}：${birthDateStr}\n` : '';
        userInfo += userZodiacInfo.zodiacSign ? `${zodiacLabel}：${userZodiacInfo.zodiacSign}\n` : '';
      }
    }
    
    // 根据语言设置不同的用户提示词
    const promptLanguage = language || user.language || 'zh-CN';
    
    // 简体中文用户提示词
    const zhCNUserPrompt = `
请使用中文回答。
${userInfo}
抽取的卡牌：
${cardsDescription}
`;

    // 繁体中文用户提示词
    const zhTWUserPrompt = `
請使用繁體中文回答。
${userInfo}
抽取的塔羅牌：
${cardsDescription}
`;

    // 英文用户提示词
    const enUserPrompt = `
Please answer in English.
User Information:
${userInfo.replace('当前日期：', 'Current Date: ')
           .replace('出生日期：', 'Birth Date: ')
           .replace('星座：', 'Zodiac Sign: ')}
Drawn Tarot Cards:
${cardsDescription}
`;

    // 日文用户提示词
    const jaUserPrompt = `
日本語で回答してください。
ユーザー情報：
${userInfo.replace('当前日期：', '現在の日付：')
           .replace('出生日期：', '生年月日：')
           .replace('星座：', '星座：')}
選ばれたタロットカード：
${cardsDescription}
`;

    // 根据语言选择对应的用户提示词
    let userPrompt;
    switch(promptLanguage) {
      case 'en':
        userPrompt = enUserPrompt;
        break;
      case 'ja':
        userPrompt = jaUserPrompt;
        break;
      case 'zh-TW':
        userPrompt = zhTWUserPrompt;
        break;
      case 'zh-CN':
      default:
        userPrompt = zhCNUserPrompt;
        break;
    }
    
    console.log('Fortune System Prompt:', systemPrompt.substring(0, 50) + '...');
    console.log('Fortune User Prompt:', userPrompt.substring(0, 50) + '...');

    // const response = await arkAPI.post('/chat/completions', {
    //   model: isYearlyFortune ? ("ep-20250214104924-l5dzk") : "ep-20250214112105-ngwzl",
    const response = await qwenAPI.post('/chat/completions', {
      model: "qwen-turbo-latest",
      messages: [
        { role: "system", content: systemPrompt },
        { role: "user", content: userPrompt }
      ],
      stream: false,
      parameters: {
        response_language: language || user.language || 'zh-CN'
      }
    });

    if (!response.data || !response.data.choices || !response.data.choices[0] || !response.data.choices[0].message) {
      console.error(`[占卜API错误] ${new Date().toISOString()} - API返回格式错误:`, JSON.stringify(response.data));
      throw new Error('API 返回格式错误');
    }

    let fortuneResult;
    try {
      // 获取API返回的内容并清理markdown格式
      const content = cleanMarkdownFormat(response.data.choices[0].message.content);
      
      // 将返回内容解析为各个部分
      const parsedContent = parseFortuneContentSections(content);
      
      // 使用翻译后的文本
      const userLang = language || user.language || 'zh-CN';
      
      // 处理解析后的数据结构
      const sections = parsedContent.sections || parsedContent;
      const ratings = parsedContent.ratings || {};
      const paragraphDetectionStatus = parsedContent.paragraphDetectionStatus !== undefined ? 
        parsedContent.paragraphDetectionStatus : null;
      
      fortuneResult = {
        reading: content.trim(),
        sections: sections,
        ratings: ratings,
        paragraphDetectionStatus: paragraphDetectionStatus
      };

      // 将结果转换为字符串
      fortuneResult = JSON.stringify(fortuneResult);
    } catch (error) {
      console.error(`[占卜API错误] ${new Date().toISOString()} - 处理占卜结果时出错:`, error);
      // 如果处理失败，返回基本格式，同样使用翻译文本
      const userLang = language || user.language || 'zh-CN';
      fortuneResult = JSON.stringify({
        reading: "抱歉，解析过程中出现了错误。",
        sections: {},
        paragraphDetectionStatus: 0 // 设置为0表示解析失败
      });
    }

    // 从response中获取token使用情况
    const inputTokens = response.data.usage?.prompt_tokens || 0;
    const outputTokens = response.data.usage?.completion_tokens || 0;

    // 更新会话中的运势预测结果和状态 - 保持只更新现有会话，不创建新记录
    try {
      await pool.query(
        'UPDATE sessions SET fortune_result = ?, status = ?, input_tokens = ?, output_tokens = ?, paragraph_detection_status = ? WHERE id = ?',
        [fortuneResult, 'completed', inputTokens, outputTokens, JSON.parse(fortuneResult).paragraphDetectionStatus, sessionId]
      );
      console.log(`[Session.update] 会话 ${sessionId} 运势预测结果已保存到数据库:`, {
        status: 'completed',
        input_tokens: inputTokens,
        output_tokens: outputTokens,
        result_length: fortuneResult.length
      });
    } catch (dbError) {
      console.error(`[Session.update] 会话 ${sessionId} 更新数据库失败:`, dbError);
      // 继续执行，不要让数据库错误影响API响应
    }

    res.json({ 
      reading: fortuneResult,
      usage: response.data.usage
    });
  } catch (error) {
    console.error(`[占卜API错误] ${new Date().toISOString()} - 生成运势预测时出现错误:`, error);
    let errorMessage = '生成运势预测时出现错误';
    
    if (error.response?.data?.error) {
      errorMessage = error.response.data.error;
      console.error(`[占卜API错误] ${new Date().toISOString()} - API错误详情:`, JSON.stringify(error.response.data, null, 2));
    } else if (error.message === 'API 返回格式错误') {
      errorMessage = 'API 返回数据格式不正确';
    } else if (error.code === 'ECONNABORTED') {
      errorMessage = '请求超时，请稍后重试';
      console.error(`[占卜API错误] ${new Date().toISOString()} - 请求超时`);
    }
    
    res.status(500).json({ error: errorMessage });
    console.error(`[占卜API错误] ${new Date().toISOString()} - 返回错误给客户端: ${errorMessage}`);
  }
});

/**
 * 根据sessionId获取运势数据
 * @route GET /api/fortune/session/:sessionId
 * @access Public
 */
router.get('/session/:sessionId', asyncHandler(async (req, res) => {
  const { sessionId } = req.params;
  
  if (!sessionId) {
    return res.status(400).json({
      success: false,
      message: '缺少sessionId参数'
    });
  }

  try {
    // 从数据库中查询会话
    const session = await Session.findById(sessionId);
    
    if (!session) {
      return res.status(404).json({
        success: false,
        message: '找不到对应的会话'
      });
    }
    
    // 提取运势结果
    let fortuneResult = {};
    if (session.fortune_result || session.readingResult) {
      try {
        // 解析运势结果
        fortuneResult = session.readingResult || session.fortune_result;
      } catch (error) {
        console.error('解析运势结果失败:', error);
      }
    }

    // 提取卡牌数据
    let cardData = [];
    if (session.selectedCards || session.selected_cards) {
      cardData = session.selectedCards || session.selected_cards || [];
    }

    // 提取牌阵数据
    let spreadData = {};
    if (session.selectedSpread || session.selected_spread) {
      spreadData = session.selectedSpread || session.selected_spread || {};
    }

    const responseData = {
      success: true,
      data: {
        fortuneResult,
        cardData,
        spreadData,
        createdAt: session.created_at || session.timestamp
      }
    };
    
    return res.json(responseData);
  } catch (error) {
    console.error('获取运势数据失败:', error);
    return res.status(500).json({
      success: false,
      message: '服务器错误，获取运势数据失败'
    });
  }
}));

module.exports = router; 