import React, { useState, useRef, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { useTranslation } from 'react-i18next';
import { useTheme } from '../contexts/ThemeContext';
import {
  socialPlatforms,
  generateShareContent,
  getUserQuestionFromStorage,
  copyToClipboard,
  openShareLink,
  isMobileDevice,
  type SocialPlatform
} from '../utils/shareUtils';
import { FaLink, FaCheck, FaUpload, FaGift, FaChevronDown } from 'react-icons/fa6';
import { SiDiscord } from 'react-icons/si';
import { submitShare, checkRewardStatus } from '../services/shareService';
import { useUser } from '../contexts/UserContext';
import CdnLazyImage from './CdnLazyImage';
import LanguageLink from './LanguageLink';

interface ShareDialogProps {
  isOpen: boolean;
  onClose: () => void;
  pageType?: 'tarot-result' | 'daily-fortune' | 'yes-no-tarot';
  customUrl?: string;
  sessionId?: string;
  hasReceivedReward?: boolean;
}

const ShareDialog: React.FC<ShareDialogProps> = ({ 
  isOpen, 
  onClose, 
  pageType = 'tarot-result',
  customUrl,
  sessionId,
  hasReceivedReward: propHasReceivedReward
}) => {
  const { t, i18n } = useTranslation();
  const { theme } = useTheme();
  const { user, refreshUser } = useUser();
  const isDark = theme === 'dark';
  
  const [copiedLink, setCopiedLink] = useState(false);
  const [copiedPlatform, setCopiedPlatform] = useState<string | null>(null);
  const [selectedPlatform, setSelectedPlatform] = useState<string | null>(null);
  const [screenshot, setScreenshot] = useState<File | null>(null);
  const [previewUrl, setPreviewUrl] = useState<string | null>(null);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [submitSuccess, setSubmitSuccess] = useState(false);
  const [submitError, setSubmitError] = useState<string | null>(null);
  const [hasReceivedReward, setHasReceivedReward] = useState(propHasReceivedReward || false);

  const [showToast, setShowToast] = useState(false);
  const [showPlatformDropdown, setShowPlatformDropdown] = useState(false);
  const [selectedPlatformName, setSelectedPlatformName] = useState<string>('');
  const [isMobile, setIsMobile] = useState(false);
  
  const fileInputRef = useRef<HTMLInputElement>(null);
  const dropdownRef = useRef<HTMLDivElement>(null);
  
  // 检测设备类型 - 结合屏幕尺寸和设备类型
  useEffect(() => {
    const checkMobile = () => {
      // 同时考虑屏幕尺寸和设备类型
      setIsMobile(window.innerWidth < 768 || isMobileDevice());
    };

    checkMobile();
    window.addEventListener('resize', checkMobile);
    return () => {
      window.removeEventListener('resize', checkMobile);
    };
  }, []);
  
  // 点击外部关闭下拉菜单
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setShowPlatformDropdown(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);
  
  // 检查用户是否已经获得过分享奖励
  useEffect(() => {
    // 如果已经从props传入了奖励状态，直接使用
    if (propHasReceivedReward !== undefined) {
      setHasReceivedReward(propHasReceivedReward);
    }
    // 否则从服务器获取
    else if (isOpen && user) {
      checkRewardStatus()
        .then(hasReceived => {
          setHasReceivedReward(hasReceived);
        })
        .catch(error => {
          console.error('获取奖励状态失败:', error);
        });
    }
  }, [isOpen, user, propHasReceivedReward]);
  
  // 获取图标文件名
  const getIconFileName = (platformId: string): string => {
    const iconMap: { [key: string]: string } = {
      facebook: 'fb.webp',
      twitter: 'twitter.webp',
      linkedin: 'linkedin.webp',
      pinterest: 'pinterest.webp',
      reddit: 'reddit.webp',
      dcard: 'Dcard.webp',
      wechat: 'pyq.webp',
      instagram: 'Instagram.webp',
      threads: 'threads.webp',
      discord: 'discord.webp',
      weibo: 'weibo.webp',
      xiaohongshu: 'xhs.webp'
    };

    return iconMap[platformId] || 'fb.webp';
  };

  // 生成分享内容（用于外部平台分享，强制使用生产环境URL）
  const userQuestion = getUserQuestionFromStorage();
  const shareContent = generateShareContent(
    pageType,
    userQuestion,
    i18n.language,
    t,
    customUrl,
    sessionId,
    true // 强制使用生产环境URL用于外部分享
  );

  // 生成本地测试用的分享内容（保持当前环境URL）
  const localShareContent = generateShareContent(
    pageType,
    userQuestion,
    i18n.language,
    t,
    customUrl,
    sessionId,
    false // 不强制转换，保持当前环境URL
  );



  // 根据语言过滤平台
  const filteredPlatforms = socialPlatforms.filter(platform => {
    if (platform.showInRegion === 'all') return true;
    if (platform.showInRegion === 'zh') return i18n.language.startsWith('zh');
    if (platform.showInRegion === 'en') return !i18n.language.startsWith('zh');
    return true;
  });

  // 复制分享链接的通用函数
  const copyShareUrl = async () => {
    const urlToCopy = localShareContent.url;
    const success = await copyToClipboard(urlToCopy);
    if (success) {
      setShowToast(true);
      setTimeout(() => setShowToast(false), 2000);
    }
    return success;
  };

  // 处理平台分享
  const handlePlatformShare = async (platform: SocialPlatform) => {
    const shareUrl = platform.shareUrl(shareContent);

    // 记录选择的平台，用于后续截图上传
    setSelectedPlatform(platform.id);
    setSelectedPlatformName(platform.name);
    setShowPlatformDropdown(false);

    // 检测是否为移动设备上的深度链接
    const isMobileDeepLink =
      shareUrl.startsWith('instagram://') ||
      shareUrl.startsWith('xhsdiscover://') ||
      shareUrl.startsWith('weixin://') ||
      shareUrl.startsWith('fb://') ||
      shareUrl.startsWith('dcard://') ||
      shareUrl.startsWith('reddit://') ||
      shareUrl.startsWith('twitter://') ||
      shareUrl.startsWith('linkedin://') ||
      shareUrl.startsWith('pinterest://') ||
      shareUrl.startsWith('discord://');

    // 所有情况下都先复制分享链接
    await copyShareUrl();

    // 如果是移动设备上的深度链接，先等待Toast显示，然后打开app
    if (isMobileDeepLink) {
      // 等待一小段时间让用户看到Toast提示
      setTimeout(() => {
        window.location.href = shareUrl;
      }, 500); // 500ms延迟，让用户看到"分享链接已复制"提示
      return;
    }

    // 如果平台设置了copyOnly，则只复制链接（已经在上面复制了）
    if (platform.copyOnly) {
      // 设置平台复制状态（用于显示平台图标上的复制成功提示）
      setCopiedPlatform(platform.id);
      setTimeout(() => setCopiedPlatform(null), 2000);
    } else {
      // 非copyOnly平台，打开分享窗口
      openShareLink(shareUrl);
    }
  };

  // 处理复制链接
  const handleCopyLink = async () => {
    const success = await copyShareUrl();
    if (success) {
      setCopiedLink(true);
      setTimeout(() => setCopiedLink(false), 2000);
    }
  };
  
  // 处理平台选择
  const handleSelectPlatform = (platform: SocialPlatform) => {
    setSelectedPlatform(platform.id);
    setSelectedPlatformName(platform.name);
    setShowPlatformDropdown(false);
  };
  
  // 处理文件选择
  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files[0]) {
      const file = e.target.files[0];
      
      // 检查文件类型
      if (!file.type.startsWith('image/')) {
        setSubmitError(t('share.error.image_only', '请选择图片文件'));
        return;
      }
      
      // 检查文件大小（限制5MB）
      if (file.size > 5 * 1024 * 1024) {
        setSubmitError(t('share.error.file_too_large', '文件大小不能超过5MB'));
        return;
      }
      
      setScreenshot(file);
      setPreviewUrl(URL.createObjectURL(file));
      setSubmitError(null);
    }
  };
  
  // 处理截图上传
  const handleSubmit = async () => {
    // 验证用户登录状态
    if (!user) {
      setSubmitError(t('share.reward.login_to_submit', '登录后才可以分享'));
      return;
    }

    // 验证平台选择
    if (!selectedPlatform) {
      setSubmitError(t('share.error.platform_required', '请选择分享平台'));
      return;
    }

    if (!screenshot) {
      setSubmitError(t('share.error.screenshot_required', '请上传分享截图'));
      return;
    }
    
    setIsSubmitting(true);
    setSubmitError(null);
    
    try {
      const formData = new FormData();
      formData.append('screenshot', screenshot);
      formData.append('platform', selectedPlatform);
      
      if (sessionId) {
        formData.append('sessionId', sessionId);
      }
      
      // 添加当前用户使用的语言
      formData.append('language', i18n.language);
      
      const result = await submitShare(formData);
      
      setSubmitSuccess(true);
      setHasReceivedReward(result.hasReceivedReward);
      
      // 刷新用户信息，以获取最新的VIP状态
      if (result.hasReceivedReward) {
        await refreshUser();
      }
    } catch (error: any) {
      console.error('提交分享失败:', error);
      setSubmitError(error.response?.data?.message || t('share.error.submit_failed', '提交失败，请重试'));
    } finally {
      setIsSubmitting(false);
    }
  };
  
  // 处理对话框关闭
  const handleClose = () => {
    // 重置状态
    setSelectedPlatform(null);
    setSelectedPlatformName('');
    setScreenshot(null);
    setPreviewUrl(null);
    setSubmitSuccess(false);
    setSubmitError(null);
    setShowPlatformDropdown(false);
    
    // 调用父组件的关闭方法
    onClose();
  };

  return (
    <AnimatePresence>
      {isOpen && (
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          className={`fixed inset-0 z-50 ${isMobile ? 'flex items-end justify-center' : ''}`}
        >
          {/* Toast提示 - 使用固定宽度和绝对定位确保水平居中 */}
          <div className="fixed top-0 left-0 w-full flex justify-center items-center z-[9999] pointer-events-none">
            <AnimatePresence>
              {showToast && (
                <motion.div
                  initial={{ opacity: 0, y: -20 }}
                  animate={{ opacity: 1, y: 60 }}
                  exit={{ opacity: 0, y: -20 }}
                  transition={{ duration: 0.3 }}
                  className={`px-4 py-2 rounded-lg shadow-lg flex items-center pointer-events-auto ${
                    isDark ? 'bg-green-700 text-white' : 'bg-green-500 text-white'
                  }`}
                >
                  <FaCheck className="mr-2" />
                  {t('share.share_link_copied', '分享链接已复制')}
                </motion.div>
              )}
            </AnimatePresence>
          </div>

          {/* 背景遮罩 - 仅在移动端显示 */}
          {isMobile && (
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 0.6 }}
              exit={{ opacity: 0 }}
              className="absolute inset-0 bg-black"
              onClick={handleClose}
            />
          )}

          {/* PC端点击外部区域关闭 */}
          {!isMobile && (
            <div
              className="absolute inset-0"
              onClick={handleClose}
            />
          )}

          {/* 侧边栏内容 */}
          <motion.div
            initial={isMobile ? { y: '100%' } : { x: '100%' }}
            animate={isMobile ? { y: 0 } : { x: 0 }}
            exit={isMobile ? { y: '100%' } : { x: '100%' }}
            transition={{ duration: 0.3, ease: 'easeInOut' }}
            onClick={(e) => e.stopPropagation()}
            className={`${isDark ? 'bg-gray-900' : 'bg-white'}
              ${isMobile
                ? 'relative w-full rounded-t-xl p-4'
                : 'absolute right-0 top-0 h-full w-[28rem] pt-16 px-6 pb-6 border-l border-gray-200 dark:border-gray-700'
              }
              shadow-2xl overflow-y-auto ${isMobile ? 'max-h-[75vh]' : ''} z-50`}
          >
            {/* 移动端顶部拖动条 */}
            {isMobile && (
              <div className="w-16 h-1 bg-gray-300 dark:bg-gray-700 rounded-full mx-auto mb-4"></div>
            )}

            {/* 标题和关闭按钮 - 仅在未提交成功时显示 */}
            {!submitSuccess && (
              <div className={`flex items-center justify-between ${isMobile ? 'mb-5' : 'mb-6'} ${isMobile ? 'pt-2' : 'pt-0'}`}>
                {/* 左侧占位，保持标题居中 */}
                <div className="w-8"></div>

                {/* 标题 */}
                <h2 className={`${isMobile ? 'text-lg' : 'text-xl'} font-bold text-center ${isDark ? 'text-white' : 'text-gray-800'} flex-1`}>
                  {t('share.title', '分享解读结果')}
                </h2>

                {/* 关闭按钮 */}
                <button
                  onClick={handleClose}
                  className={`p-2 rounded-full ${
                    isDark ? 'text-gray-400 hover:text-gray-300 hover:bg-gray-800' : 'text-gray-500 hover:text-gray-700 hover:bg-gray-100'
                  } transition-all focus:outline-none`}
                  aria-label="关闭"
                >
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                  </svg>
                </button>
              </div>
            )}
            
            {/* 分享平台网格 - 仅在未提交成功时显示 */}
            {!submitSuccess && (
              <div className={`${isMobile ? 'mb-4' : 'mb-6'}`}>
                <div className={`grid ${isMobile ? 'grid-cols-5' : 'grid-cols-4'} gap-3 ${isMobile ? 'px-2' : 'px-0'}`}>
                  {filteredPlatforms.map((platform) => (
                    <button
                      key={platform.id}
                      onClick={() => handlePlatformShare(platform)}
                      className="flex flex-col items-center justify-center transition-opacity duration-200 hover:opacity-80"
                    >
                      <div className="mb-2 sm:mb-3 relative">
                        {platform.id === 'discord' ? (
                          <SiDiscord
                            className="w-8 h-8 sm:w-10 sm:h-10"
                            style={{ color: platform.color }}
                          />
                        ) : (
                          <CdnLazyImage
                            src={`images-optimized/social-media-icons/${getIconFileName(platform.id)}`}
                            alt={platform.name}
                            className="w-8 h-8 sm:w-10 sm:h-10 object-contain"
                          />
                        )}
                        
                        {/* 复制成功提示 */}
                        {copiedPlatform === platform.id && (
                          <div className="absolute -top-1 -right-1 bg-green-500 text-white rounded-full p-1 w-5 h-5 flex items-center justify-center">
                            <FaCheck className="w-3 h-3" />
                          </div>
                        )}
                      </div>
                      <span className={`text-xs font-medium ${isDark ? 'text-gray-300' : 'text-gray-700'}`}>
                        {platform.name}
                      </span>
                    </button>
                  ))}
                  
                  {/* 复制链接按钮（作为图标） */}
                  <button
                    onClick={handleCopyLink}
                    className="flex flex-col items-center justify-center transition-opacity duration-200 hover:opacity-80"
                  >
                    <div className="mb-2 sm:mb-3 relative">
                      <div 
                        className={`w-8 h-8 sm:w-10 sm:h-10 rounded-full flex items-center justify-center ${
                          isDark ? 'bg-gray-700' : 'bg-gray-200'
                        }`}
                      >
                        <FaLink className={`w-4 h-4 sm:w-5 sm:h-5 ${isDark ? 'text-gray-300' : 'text-gray-700'}`} />
                      </div>
                      
                      {/* 复制成功提示 */}
                      {copiedLink && (
                        <div className="absolute -top-1 -right-1 bg-green-500 text-white rounded-full p-1 w-5 h-5 flex items-center justify-center">
                          <FaCheck className="w-3 h-3" />
                        </div>
                      )}
                    </div>
                    <span className={`text-xs font-medium ${isDark ? 'text-gray-300' : 'text-gray-700'}`}>
                      {t('share.copy_link_short', '复制链接')}
                    </span>
                  </button>
                </div>
              </div>
            )}
            
            {/* 奖励部分 - 直接展示 */}
            <motion.div
              initial={{ opacity: 1, height: 'auto' }}
              animate={{ opacity: 1, height: 'auto' }}
              transition={{ duration: 0 }}
              className={`${submitSuccess ? '' : 'mt-4 sm:mt-6 border-t border-gray-200 dark:border-gray-700 pt-3 sm:pt-4'}`}
            >
              {submitSuccess ? (
                <div className={`flex flex-col justify-center text-left p-6 rounded-lg min-h-[200px]`}>
                  <div className={`flex items-center mb-4`}>
                    <div className={`w-16 h-16 rounded-full flex items-center justify-center mr-4 ${isDark ? 'bg-green-900/30' : 'bg-green-100'}`}>
                      <FaCheck className={`w-8 h-8 ${isDark ? 'text-green-400' : 'text-green-600'}`} />
                    </div>
                    <h4 className={`font-bold text-2xl ${isDark ? 'text-green-400' : 'text-green-600'}`}>
                      {t('share.reward.submit_success_title', '提交成功')}
                    </h4>
                  </div>
                  <p className={`text-base mb-3 leading-relaxed ${isDark ? 'text-gray-300' : 'text-gray-700'}`}>
                    {t('share.reward.submit_success_desc', '我们将在1-3个工作日内完成审核，通过后将自动获得7天VIP奖励。')}
                  </p>
                  <p className={`text-base ${isDark ? 'text-gray-400' : 'text-gray-500'}`}>
                    {t('share.reward.check_status_prefix', '您可以在')}
                    <LanguageLink
                      to="/user-card"
                      className={`cursor-pointer underline ${isDark ? '!text-purple-400' : '!text-purple-600'}`}
                    >
                      {t('share.reward.check_status_link', '个人中心')}
                    </LanguageLink>
                    {t('share.reward.check_status_suffix', '页面查看审核进度')}
                  </p>
                </div>
              ) : (
                <div className={`p-3 sm:p-4 rounded-lg ${
                  isDark ? 'bg-purple-900/20' : 'bg-purple-50'
                } mb-3 sm:mb-4`}>
                  {!submitSuccess && (
                    <div className="flex items-center mb-3">
                      <FaGift className={`w-5 h-5 mr-2 ${isDark ? 'text-purple-400' : 'text-purple-600'}`} />
                      <h3 className={`font-bold ${isDark ? 'text-purple-300' : 'text-purple-700'}`}>
                        {hasReceivedReward 
                          ? t('share.reward.already_received', '您已获得过分享奖励') 
                          : t('share.reward.title', '获取7天免费VIP')}
                      </h3>
                    </div>
                  )}
                  
                  {hasReceivedReward ? (
                    <p className={`text-sm ${isDark ? 'text-gray-300' : 'text-gray-700'}`}>
                      {t('share.reward.already_received_desc', '每个账号只能获得一次分享奖励，感谢您的支持！')}
                    </p>
                  ) : (
                    <>
                      <div className={`text-base mb-3 ${isDark ? 'text-gray-300' : 'text-gray-700'}`}>
                        <p className="mb-1">
                          <span className="font-bold">Step 1.</span> {t('share.reward.step1_new', '点击「复制链接」按钮复制解读页面地址')}
                        </p>
                        <p className="mb-1">
                          <span className="font-bold">Step 2.</span> {t('share.reward.step2_new', '在社交媒体分享解读截图并附带解读页面地址')}
                        </p>
                        <p className="mb-1">
                          <span className="font-bold">Step 3.</span> {t('share.reward.step3_new', '保留分享帖子至少2h')}
                        </p>
                        <p className="mb-2">
                          <span className="font-bold">Step 4.</span> {t('share.reward.step4_new', '上传包含上述内容的分享帖子截图用于审核')}
                        </p>
                        <p className="font-medium text-purple-600 dark:text-purple-400">
                          {t('share.reward.final_note', '审核通过后获得7天免费VIP！')}
                        </p>
                      </div>
                      
                      {/* 分享平台选择器 - 新增 */}
                      <div className="mb-3 sm:mb-4">
                        <label className={`block text-base font-medium mb-1 ${isDark ? 'text-gray-300' : 'text-gray-700'}`}>
                          {t('share.reward.platform', '分享平台')}
                        </label>
                        <div className="relative" ref={dropdownRef}>
                          <button
                            type="button"
                            onClick={() => setShowPlatformDropdown(!showPlatformDropdown)}
                            className={`w-full flex items-center justify-between p-2 rounded-lg border ${
                              isDark 
                                ? 'bg-gray-800 border-gray-700 text-gray-200' 
                                : 'bg-white border-gray-300 text-gray-800'
                            } focus:outline-none focus:ring-2 focus:ring-purple-500`}
                          >
                            <span>
                              {selectedPlatformName || t('share.reward.select_platform', '选择分享平台')}
                            </span>
                            <FaChevronDown className={`w-4 h-4 transition-transform ${showPlatformDropdown ? 'transform rotate-180' : ''}`} />
                          </button>
                          
                          {/* 下拉菜单 */}
                          {showPlatformDropdown && (
                            <div className={`absolute z-10 mt-1 w-full rounded-md shadow-lg ${
                              isDark ? 'bg-gray-800' : 'bg-white'
                            } ring-1 ring-black ring-opacity-5 ${isMobile ? 'max-h-48' : 'max-h-60'} overflow-auto`}>
                              <div className="py-1">
                                {filteredPlatforms.map((platform) => (
                                  <button
                                    key={platform.id}
                                    onClick={() => handleSelectPlatform(platform)}
                                    className={`w-full text-left px-4 py-2 flex items-center ${
                                      isDark 
                                        ? 'hover:bg-gray-700 text-gray-200' 
                                        : 'hover:bg-gray-100 text-gray-800'
                                    }`}
                                  >
                                    {platform.id === 'discord' ? (
                                      <SiDiscord className="w-5 h-5 mr-3" style={{ color: platform.color }} />
                                    ) : (
                                      <CdnLazyImage
                                        src={`images-optimized/social-media-icons/${getIconFileName(platform.id)}`}
                                        alt={platform.name}
                                        className="w-5 h-5 mr-3 object-contain"
                                      />
                                    )}
                                    {platform.name}
                                    {selectedPlatform === platform.id && (
                                      <FaCheck className="ml-auto text-green-500" />
                                    )}
                                  </button>
                                ))}
                              </div>
                            </div>
                          )}
                        </div>
                      </div>
                      

                      {/* 文件上传区域 */}
                      <div className="mt-3">
                        <label className={`block text-base font-medium mb-1 ${isDark ? 'text-gray-300' : 'text-gray-700'}`}>
                          {t('share.reward.screenshot', '分享截图')}
                        </label>
                        <input
                          type="file"
                          ref={fileInputRef}
                          onChange={handleFileChange}
                          accept="image/*"
                          className="hidden"
                        />
                        
                        {previewUrl ? (
                          <div className="relative mb-2 sm:mb-3">
                            <img 
                              src={previewUrl} 
                              alt="Preview" 
                              className="w-full h-auto rounded-lg border border-gray-300 dark:border-gray-700" 
                            />
                            <button
                              onClick={() => {
                                setScreenshot(null);
                                setPreviewUrl(null);
                              }}
                              className="absolute top-2 right-2 bg-red-500 text-white rounded-full p-1 w-6 h-6 flex items-center justify-center"
                            >
                              <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                              </svg>
                            </button>
                          </div>
                        ) : (
                          <button
                            onClick={() => fileInputRef.current?.click()}
                            className={`w-full flex items-center justify-center p-4 rounded-lg border-2 border-dashed ${
                              isDark 
                                ? 'border-gray-600 hover:border-purple-500 bg-gray-800/50' 
                                : 'border-gray-300 hover:border-purple-500 bg-gray-50'
                            } transition-colors`}
                          >
                            <FaUpload className={`w-5 h-5 mr-2 ${isDark ? 'text-gray-400' : 'text-gray-500'}`} />
                            <span className={`${isDark ? 'text-gray-300' : 'text-gray-600'}`}>
                              {t('share.reward.upload_screenshot', '上传分享截图')}
                            </span>
                          </button>
                        )}
                        
                        {/* 错误提示 */}
                        {submitError && (
                          <div className="mt-2 text-red-500 text-sm">
                            {submitError}
                          </div>
                        )}
                        
                        {/* 提交按钮 */}
                        <button
                          onClick={handleSubmit}
                          disabled={!user || (!selectedPlatform) || (!screenshot || isSubmitting)}
                          className={`mt-3 w-full py-2 px-4 rounded-lg font-medium ${
                            !user || (!selectedPlatform) || (!screenshot || isSubmitting)
                              ? 'bg-gray-400 cursor-not-allowed'
                              : 'bg-purple-600 hover:bg-purple-700 text-white'
                          }`}
                        >
                          {isSubmitting ? (
                            <span className="flex items-center justify-center">
                              <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                                <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 714 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                              </svg>
                              {t('share.reward.submitting', '提交中...')}
                            </span>
                          ) : !user ? (
                            t('share.reward.login_to_submit', '登录后才可以分享')
                          ) : (
                            t('share.reward.submit', '提交获取奖励')
                          )}
                        </button>
                      </div>
                    </>
                  )}
                </div>
              )}
            </motion.div>
          </motion.div>
        </motion.div>
      )}
    </AnimatePresence>
  );
};

export default ShareDialog;
