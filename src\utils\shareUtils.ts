/**
 * 分享工具函数
 */

export interface ShareContent {
  url: string;
  title: string;
  description: string;
}

export interface SocialPlatform {
  id: string;
  name: string;
  icon: string;
  color: string;
  shareUrl: (content: ShareContent) => string;
  showInRegion?: 'all' | 'zh' | 'en';
  copyOnly?: boolean; // 是否只复制链接而不打开分享窗口
}

/**
 * 获取当前页面URL，并添加分享参数
 */
export const getCurrentPageUrl = (addSharedParam: boolean = false, sessionId?: string, forceProduction: boolean = false): string => {
  let currentUrl = window.location.href;

  // 只有在明确要求转换为生产环境URL时才进行转换（比如分享到外部平台）
  if (forceProduction && (currentUrl.includes('localhost') || currentUrl.includes('127.0.0.1'))) {
    currentUrl = currentUrl.replace(/https?:\/\/(localhost|127\.0\.0\.1)(:\d+)?/, 'https://tarotqa.com');
  }

  // 如果需要添加分享参数
  if (addSharedParam) {
    const url = new URL(currentUrl);
    url.searchParams.set('shared', 'true');

    // 如果提供了sessionId，添加到URL参数中
    if (sessionId) {
      url.searchParams.set('sessionId', sessionId);
    }

    return url.toString();
  }

  return currentUrl;
};

/**
 * 从localStorage获取用户问题
 */
export const getUserQuestionFromStorage = (): string => {
  return localStorage.getItem('userQuestion') || '';
};

/**
 * 根据页面类型生成分享内容
 */
export const generateShareContent = (
  pageType: 'tarot-result' | 'daily-fortune' | 'yes-no-tarot',
  userQuestion: string,
  language: string,
  _t: (key: string) => string,
  url?: string,
  sessionId?: string,
  forceProduction: boolean = true // 默认为true，用于外部分享
): ShareContent => {
  const currentUrl = url || getCurrentPageUrl(true, sessionId, forceProduction); // 添加分享参数和sessionId
  const isZh = language.startsWith('zh');
  
  let title = '';
  let description = '';
  
  switch (pageType) {
    case 'tarot-result':
      if (isZh) {
        title = userQuestion ? `${userQuestion} - TarotQA塔罗解读` : 'TarotQA塔罗解读';
        description = '通过AI塔罗师获得专业的塔罗牌解读，探索内心的智慧与指引。';
      } else {
        title = userQuestion ? `${userQuestion} - TarotQA Tarot Reading` : 'TarotQA Tarot Reading';
        description = 'Get professional tarot card readings through AI tarot readers, explore inner wisdom and guidance.';
      }
      break;
      
    case 'daily-fortune':
      if (isZh) {
        title = '每日星座运势 - TarotQA塔罗解读';
        description = '每日塔罗运势解读，了解今日的整体运势、爱情、事业、财富和健康指引。';
      } else {
        title = 'Daily Horoscope Fortune - TarotQA Tarot Reading';
        description = 'Daily tarot fortune reading, understand today\'s overall fortune, love, career, wealth and health guidance.';
      }
      break;
      
    case 'yes-no-tarot':
      if (isZh) {
        title = userQuestion ? `${userQuestion} - TarotQA是否塔罗占卜` : 'TarotQA是否塔罗占卜';
        description = '通过是否塔罗占卜获得明确的答案指引，帮助你做出重要决定。';
      } else {
        title = userQuestion ? `${userQuestion} - TarotQA Yes/No Tarot Divination` : 'TarotQA Yes/No Tarot Divination';
        description = 'Get clear answers through Yes/No tarot divination to help you make important decisions.';
      }
      break;
      
    default:
      if (isZh) {
        title = 'TarotQA - AI塔罗解读';
        description = '专业的AI塔罗解读服务，探索内心的智慧与指引。';
      } else {
        title = 'TarotQA - AI Tarot Reading';
        description = 'Professional AI tarot reading service, explore inner wisdom and guidance.';
      }
  }
  
  return {
    url: currentUrl,
    title,
    description
  };
};

/**
 * 复制文本到剪贴板
 */
export const copyToClipboard = async (text: string): Promise<boolean> => {
  try {
    if (navigator.clipboard && window.isSecureContext) {
      await navigator.clipboard.writeText(text);
      return true;
    } else {
      // 降级方案
      const textArea = document.createElement('textarea');
      textArea.value = text;
      textArea.style.position = 'fixed';
      textArea.style.left = '-999999px';
      textArea.style.top = '-999999px';
      document.body.appendChild(textArea);
      textArea.focus();
      textArea.select();
      const result = document.execCommand('copy');
      document.body.removeChild(textArea);
      return result;
    }
  } catch (error) {
    // console.log('复制到剪贴板失败:', error);
    return false;
  }
};
/**
 * 检测是否为移动设备
 */
export const isMobileDevice = (): boolean => {
  return /iPhone|iPad|iPod|Android/i.test(navigator.userAgent);
};

/**
 * 打开分享链接
 */
export const openShareLink = (url: string): void => {
  window.open(url, '_blank', 'width=600,height=400,scrollbars=yes,resizable=yes');
};

/**
 * 社交平台配置
 */
export const socialPlatforms: SocialPlatform[] = [
  {
    id: 'facebook',
    name: 'Facebook',
    icon: 'FaFacebook',
    color: '#1877F2',
    shareUrl: ({ url, title, description }) => {
      if (isMobileDevice()) {
        // 使用Facebook app深度链接
        return `fb://share?href=${encodeURIComponent(url)}`;
      } else {
        // 桌面设备使用网页版
        return `https://www.facebook.com/sharer/sharer.php?u=${encodeURIComponent(url)}&quote=${encodeURIComponent(title + ' - ' + description)}`;
      }
    },
    showInRegion: 'all'
  },
  {
    id: 'dcard',
    name: 'Dcard',
    icon: 'dcard',
    color: '#006AA6',
    shareUrl: ({ url }) => {
      if (isMobileDevice()) {
        // 使用Dcard app深度链接
        return `dcard://share?url=${encodeURIComponent(url)}`;
      } else {
        // 桌面设备使用网页版
        return `https://www.dcard.tw/f?share=${encodeURIComponent(url)}`;
      }
    },
    showInRegion: 'zh'
  },
  {
    id: 'reddit',
    name: 'Reddit',
    icon: 'FaReddit',
    color: '#FF4500',
    shareUrl: ({ url, title }) => {
      if (isMobileDevice()) {
        // 使用Reddit app深度链接
        return `reddit://submit?url=${encodeURIComponent(url)}&title=${encodeURIComponent(title)}`;
      } else {
        // 桌面设备使用网页版
        return `https://reddit.com/submit?url=${encodeURIComponent(url)}&title=${encodeURIComponent(title)}`;
      }
    },
    showInRegion: 'all'
  },
  {
    id: 'wechat',
    name: '朋友圈',
    icon: 'wechat',
    color: '#07C160',
    shareUrl: ({ url }) => {
      if (isMobileDevice()) {
        // 在移动端打开微信主界面
        return `weixin://`;
      } else {
        // 桌面设备上只能复制链接
        return url;
      }
    },
    showInRegion: 'zh',
    copyOnly: true // 微信分享需要用户手动操作
  },
  {
    id: 'twitter',
    name: 'Twitter',
    icon: 'FaXTwitter',
    color: '#000000',
    shareUrl: ({ url, title }) => {
      if (isMobileDevice()) {
        // 使用Twitter app深度链接
        return `twitter://post?message=${encodeURIComponent(title + ' ' + url + ' #tarot #divination')}`;
      } else {
        // 桌面设备使用网页版
        return `https://twitter.com/intent/tweet?url=${encodeURIComponent(url)}&text=${encodeURIComponent(title)}&hashtags=tarot,divination`;
      }
    },
    showInRegion: 'all'
  },

  {
    id: 'linkedin',
    name: 'LinkedIn',
    icon: 'FaLinkedin',
    color: '#0A66C2',
    shareUrl: ({ url }) => {
      if (isMobileDevice()) {
        // 使用LinkedIn app深度链接
        return `linkedin://sharing/share-offsite/?url=${encodeURIComponent(url)}`;
      } else {
        // 桌面设备使用网页版
        return `https://www.linkedin.com/sharing/share-offsite/?url=${encodeURIComponent(url)}`;
      }
    },
    showInRegion: 'all'
  },
  {
    id: 'pinterest',
    name: 'Pinterest',
    icon: 'FaPinterest',
    color: '#E60023',
    shareUrl: ({ url, title }) => {
      if (isMobileDevice()) {
        // 使用Pinterest app深度链接
        return `pinterest://create/pin/?url=${encodeURIComponent(url)}&description=${encodeURIComponent(title)}`;
      } else {
        // 桌面设备使用网页版
        return `https://pinterest.com/pin/create/button/?url=${encodeURIComponent(url)}&description=${encodeURIComponent(title)}`;
      }
    },
    showInRegion: 'all'
  },
  {
    id: 'discord',
    name: 'Discord',
    icon: 'discord',
    color: '#5865F2',
    shareUrl: ({ url, title }) => {
      if (isMobileDevice()) {
        // 使用Discord app深度链接
        return `discord://share?text=${encodeURIComponent(title + ' ' + url)}`;
      } else {
        // 桌面设备使用网页版
        return `https://discord.com/channels/@me?message=${encodeURIComponent(title + ' ' + url)}`;
      }
    },
    showInRegion: 'all'
  },
  {
    id: 'instagram',
    name: 'Instagram',
    icon: 'FaInstagram',
    color: '#E4405F',
    shareUrl: ({ url, title }) => {
      if (isMobileDevice()) {
        // 使用Instagram正确的深度链接格式
        return `instagram://share?text=${encodeURIComponent(title + ' ' + url)}`;
      } else {
        // 桌面设备上仍然使用复制链接方式
        return url;
      }
    },
    showInRegion: 'all',
    copyOnly: true // 在桌面设备上仍然需要复制链接
  },
  {
    id: 'threads',
    name: 'Threads',
    icon: 'FaThreads',
    color: '#000000',
    shareUrl: ({ url, title }) => {
      // 使用统一的 Threads Web Intent URL，在移动端会自动打开app，桌面端打开网页
      return `https://www.threads.net/intent/post?text=${encodeURIComponent(title + ' ' + url)}`;
    },
    showInRegion: 'all'
  },
  {
    id: 'xiaohongshu',
    name: '小红书',
    icon: 'xiaohongshu',
    color: '#FF2442',
    shareUrl: ({ url }) => {
      if (isMobileDevice()) {
        // 使用小红书发笔记的深度链接
        return `xhsdiscover://post_new_note`;
      } else {
        // 桌面设备上复制链接让用户手动分享
        return url;
      }
    },
    showInRegion: 'zh',
    copyOnly: true // 小红书分享需要用户手动操作
  }
];

