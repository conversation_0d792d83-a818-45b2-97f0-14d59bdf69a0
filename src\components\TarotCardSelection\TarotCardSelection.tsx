import React, { useState, useEffect, useRef, MouseEvent, useCallback } from 'react';
import { motion } from 'framer-motion';
import { TAROT_CARDS } from '../../data/tarot-cards';
import { checkAnonymousEligibility } from '../../services/anonymousService';
import { getBrowserFingerprint } from '../../utils/fingerprint';
import { useTranslation } from 'react-i18next';
import { useUser } from '../../contexts/UserContext';
import { useTheme } from '../../contexts/ThemeContext';
import LandingBackground from '../LandingBackground';
import VipPromptDialog from '../VipPromptDialog';
import LoginPrompt from '../LoginPrompt';
import Footer from '../Footer';
import CardSelectionBottomSheet from '../CardSelectionBottomSheet';
import axiosInstance from '../../utils/axios';
import SEO from '../SEO';
import { useLanguageNavigate } from '../../hooks/useLanguageNavigate';
import { getImageUrl } from '../../utils/cdnImageUrl';

// 导入拆分出的组件
import FloatingHint from './FloatingHint';
import SelectionModeToggle from './SelectionModeToggle';
import SlideControls from './SlideControls';
import NumberInput from './NumberInput';
import CardDeck from './CardDeck';
import ActionButton from './ActionButton';
import SpreadContainer from './SpreadContainer';
import TarotCardSelectionStyles from './TarotCardSelectionStyles';

const TarotCardSelection: React.FC = () => {
  const { t, i18n } = useTranslation();
  const { navigate } = useLanguageNavigate();
  const { user } = useUser();
  const { theme } = useTheme();
  const isDark = theme === 'dark';
  
  // 移除pageReady状态和相关代码
  const [initialLoadDone, setInitialLoadDone] = useState(false);
  
  // 修改spread状态的初始化
  const [spread, setSpread] = useState<any>(null);
  
  // 添加一个ref来保存navigate函数，避免useEffect依赖问题
  const navigateRef = useRef(navigate);
  
  // 更新navigateRef当navigate变化时
  useEffect(() => {
    navigateRef.current = navigate;
  }, [navigate]);
  
  // 检测是否为移动设备 - 在组件级别定义，避免重复计算
  const isMobile = typeof window !== 'undefined' && window.innerWidth < 640;
  
  // 定义动画持续时间常量
  const ANIMATION_DURATION = {
    flip: isMobile ? 0.2 : 0.3,             // 翻转动画时间
    scrollDelay: isMobile ? 250 : 350,       // 滚动后延迟时间
    resetProcessing: isMobile ? 200 : 300    // 重置处理状态时间
  };
  
  // 卡牌相关状态
  const [selectedCards, setSelectedCards] = useState<number[]>([]);
  const [cardImages, setCardImages] = useState<Map<number, string>>(new Map());
  const [flippedCards, setFlippedCards] = useState<number[]>([]);
  const [processingCards, setProcessingCards] = useState<Set<number>>(new Set());
  const [cardOrientations, setCardOrientations] = useState<Map<number, boolean>>(new Map());
  const [showVipPrompt, setShowVipPrompt] = useState(false);
  const [showLoginPrompt, setShowLoginPrompt] = useState(false);
  const [numberInputValue, setNumberInputValue] = useState('');
  const [numberInputError, setNumberInputError] = useState('');
  
  // 添加回scrollRef的定义
  const scrollRef = useRef<HTMLDivElement>(null);
  
  // 状态相关refs
  const numberInputErrorTimeoutRef = useRef<NodeJS.Timeout>();
  const floatingHintTimeoutRef = useRef<NodeJS.Timeout>();
  
  // 卡背图片设置 - 使用已知存在的默认图片路径
  const [cardBackImage, setCardBackImage] = useState<string>('/images-optimized/back/001.webp');
  
  // 添加拖动状态
  const [isDragging, setIsDragging] = useState(false);
  const [startX, setStartX] = useState(0);
  const [scrollLeft, setScrollLeft] = useState(0);
  const [dragActive, setDragActive] = useState(false);

  // 添加自定义选牌模式
  const [selectionMode, setSelectionMode] = useState<'slide' | 'number' | 'custom'>('slide');
  const [showCardSelector, setShowCardSelector] = useState(false);
  const [selectedPosition, setSelectedPosition] = useState<number | null>(null);

  // 添加随机排列的牌索引状态
  const [randomCardIndices, setRandomCardIndices] = useState<number[]>([]);

  // 获取当前语言
  const currentLanguage = i18n.language;
  
  // 修改requiredCards的获取逻辑
  const requiredCards = spread?.cardCount ?? 3;

  // 获取翻译后的牌阵信息
  const getTranslatedSpreadInfo = () => {
    if (!spread) return null;
    
    // 对于年运牌阵，直接使用原始信息
    if (spread.id === 'yearly-fortune') {
      return {
        name: spread.name,
        description: spread.description,
        positions: spread.positions
      };
    }
    
    const spreadId = spread.id.replace(/-/g, '_'); // 将 id 中的连字符替换为下划线
    if (Object.keys(t('spreads', { returnObjects: true })).includes(spreadId)) {
      // 如果在翻译文件中存在该牌阵的翻译
      return {
        name: t(`spreads.${spreadId}.name`),
        description: t(`spreads.${spreadId}.description`),
        positions: spread.positions.map((_: string, index: number) => {
          const positionKeys = Object.keys(t(`spreads.${spreadId}.positions`, { returnObjects: true }));
          return t(`spreads.${spreadId}.positions.${positionKeys[index]}`);
        })
      };
    }
    // 如果没有翻译，返回原始值
    return {
      name: spread.name,
      description: spread.description,
      positions: spread.positions
    };
  };

  const translatedSpreadInfo = getTranslatedSpreadInfo();

  // 检查用户权限
  const checkUserPermission = useCallback(async () => {
    if (!user) {
      // 匿名用户需要检查是否已使用过免费机会
      try {
        const fingerprint = await getBrowserFingerprint();
        const eligibility = await checkAnonymousEligibility(fingerprint);
        if (!eligibility.canUse) {
          setShowLoginPrompt(true);
          return false;
        }
        return true;
      } catch (error) {
        console.error('检查匿名用户权限失败:', error);
        setShowLoginPrompt(true);
        return false;
      }
    }
    if (user.vipStatus === 'active') return true;
    if (user.remainingReads <= 0) {
      setShowVipPrompt(true);
      return false;
    }
    return true;
  }, [user]);

  // 添加useEffect来处理spread的初始化
  useEffect(() => {
    try {
      const spreadData = JSON.parse(localStorage.getItem('selectedSpread') || 'null');
      
      if (spreadData) {
        setSpread(spreadData);
        
        // 设置一个短延迟来预加载关键资源
        const timer = setTimeout(() => {
          setInitialLoadDone(true);
        }, 100);
        
        return () => clearTimeout(timer);
      } else {
        // console.warn('未找到牌阵配置');
        // 不再重定向到spreads页面，而是使用默认牌阵 - 使用time_flow牌阵(三张牌牌阵)的翻译
        const defaultSpread = {
          id: 'time_flow',
          name: t('spreads.time_flow.name'),
          description: t('spreads.time_flow.description'),
          cardCount: 3,
          positions: [
            t('spreads.time_flow.positions.past'),
            t('spreads.time_flow.positions.present'),
            t('spreads.time_flow.positions.future')
          ]
        };
        setSpread(defaultSpread);
        
        // 保存默认牌阵到本地存储
        localStorage.setItem('selectedSpread', JSON.stringify(defaultSpread));
        
        // 设置页面就绪状态
        const timer = setTimeout(() => {
          setInitialLoadDone(true);
        }, 100);
        
        return () => clearTimeout(timer);
      }
    } catch (error) {
      // console.error('解析牌阵配置失败:', error);
      // 不再重定向到spreads页面，而是使用默认牌阵 - 使用time_flow牌阵(三张牌牌阵)的翻译
      const defaultSpread = {
        id: 'time_flow',
        name: t('spreads.time_flow.name'),
        description: t('spreads.time_flow.description'),
        cardCount: 3,
        positions: [
          t('spreads.time_flow.positions.past'),
          t('spreads.time_flow.positions.present'),
          t('spreads.time_flow.positions.future')
        ]
      };
      setSpread(defaultSpread);
      
      // 保存默认牌阵到本地存储
      localStorage.setItem('selectedSpread', JSON.stringify(defaultSpread));
      
      // 设置页面就绪状态
      const timer = setTimeout(() => {
        setInitialLoadDone(true);
      }, 100);
      
      return () => clearTimeout(timer);
    }
  }, [t]); // 添加t作为依赖项

  // 预加载资源并设置页面就绪状态
  useEffect(() => {
    if (!initialLoadDone) return;
    
    // 预加载关键资源
    const preloadImages = async () => {
      try {
        // 使用getImageUrl处理图片路径
        const backImgPath = getImageUrl('/images-optimized/back/001.webp');
        
        // 预加载卡背图片
        const backImg = new Image();
        backImg.src = backImgPath;
        
      } catch (err) {
        // console.error('预加载资源出错:', err);
        // 移除setPageReady的调用
      }
    };
    
    preloadImages();
  }, [initialLoadDone]);

  // Load card images
  useEffect(() => {
    const loadCardImages = () => {
      const newCardImageMap = new Map<number, string>();
      TAROT_CARDS.forEach((card, cardId) => {
        const imageName = `${card.nameEn.replace(/ /g, '_')}.webp`;
        const imagePath = `/images-optimized/tarot/${imageName}`;
        // 使用CDN图片URL处理
        const processedImagePath = getImageUrl(imagePath);
        newCardImageMap.set(cardId, processedImagePath);
      });
      setCardImages(newCardImageMap);
    };

    loadCardImages();
  }, []);

  // 从用户设置或本地存储加载卡背图像
  useEffect(() => {
    // 尝试从本地存储获取卡背设置
    const userCardBackSetting = localStorage.getItem('selectedCardBack') || '001';
    
    // 确保卡背ID格式正确（三位数字）
    const formattedCardBackId = userCardBackSetting.padStart(3, '0');
    
    // 设置卡背图像路径 - 确保路径格式正确
    const cardBackPath = `/images-optimized/back/${formattedCardBackId}.webp`;
    
    // 使用CDN图片URL处理
    const processedCardBackPath = getImageUrl(cardBackPath);
    
    // 设置卡背图片
    setCardBackImage(processedCardBackPath);
    
    // 添加事件监听器来捕获卡背设置变更
    const handleStorageChange = (e: StorageEvent) => {
      if (e.key === 'selectedCardBack') {
        const newCardBack = e.newValue || '001';
        // 确保卡背ID格式正确（三位数字）
        const formattedNewCardBackId = newCardBack.padStart(3, '0');
        const newPath = `/images-optimized/back/${formattedNewCardBackId}.webp`;
        
        // 使用CDN图片URL处理
        const processedNewPath = getImageUrl(newPath);
        
        // 设置新的卡背图片
        setCardBackImage(processedNewPath);
      }
    };
    
    window.addEventListener('storage', handleStorageChange);
    
    return () => {
      window.removeEventListener('storage', handleStorageChange);
    };
  }, []);

  // 洗牌和准备卡牌数组
  useEffect(() => {
    // 创建一个包含所有卡牌索引的数组
    const allCardIndices = Array.from({ length: TAROT_CARDS.length }, (_, i) => i);
    
    // 使用 Fisher-Yates 算法打乱数组
    for (let i = allCardIndices.length - 1; i > 0; i--) {
      const j = Math.floor(Math.random() * (i + 1));
      [allCardIndices[i], allCardIndices[j]] = [allCardIndices[j], allCardIndices[i]];
    }

    // 将打乱后的索引数组保存到状态中
    setRandomCardIndices(allCardIndices);
  }, []);

  // 修改恢复卡牌状态的 useEffect
  useEffect(() => {
    try {
      const savedCards = localStorage.getItem('selectedCardsInfo');
      if (savedCards) {
        const { selected, flipped, orientations, completed } = JSON.parse(savedCards);
        
        // 如果是从结果页面返回（completed为true），且spread和requiredCards匹配，则恢复状态
        if (completed && selected.length === requiredCards) {
          setSelectedCards(selected);
          setFlippedCards(flipped);
          setCardOrientations(new Map(orientations));
        } 
        // 如果是正在选牌的状态（completed为false或不存在），直接恢复
        else if (!completed) {
          setSelectedCards(selected);
          setFlippedCards(flipped);
          setCardOrientations(new Map(orientations));
        }
      }
    } catch (error) {
      // console.error('Error restoring cards from localStorage:', error);
    }
  }, [requiredCards]);

  // 修改保存卡牌状态的 useEffect
  useEffect(() => {
    if (selectedCards.length > 0) {
      const savedCards = localStorage.getItem('selectedCardsInfo');
      let completed = false;
      
      // 保持completed状态
      if (savedCards) {
        try {
          const { completed: wasCompleted } = JSON.parse(savedCards);
          completed = wasCompleted;
        } catch (error) {
          // console.error('Error parsing savedCards:', error);
        }
      }

      const cardsInfo = {
        selected: selectedCards,
        flipped: flippedCards,
        orientations: Array.from(cardOrientations.entries()),
        completed
      };
      localStorage.setItem('selectedCardsInfo', JSON.stringify(cardsInfo));
    }
  }, [selectedCards, flippedCards, cardOrientations]);

  const handleCardFlip = async (cardId: number) => {
    // 如果这张牌已经翻开了，不做任何操作
    if (flippedCards.includes(cardId)) return;

    // 如果这张牌正在处理动画，不做任何操作
    if (processingCards.has(cardId)) return;

    // 检查用户权限
    const hasPermission = await checkUserPermission();
    if (!hasPermission) return;
    
    // 修改检查逻辑：确保只有在已翻开的牌数量已经等于所需牌数时才阻止选牌
    if (flippedCards.length >= requiredCards) {
      // 清除之前的定时器
      if (floatingHintTimeoutRef.current) {
        clearTimeout(floatingHintTimeoutRef.current);
      }
      
      // 设置错误信息
      setNumberInputError(t('reading.shuffle.number_error.enough_cards'));
      
      // 2秒后隐藏提示
      floatingHintTimeoutRef.current = setTimeout(() => {
        setNumberInputError('');
      }, 2000);
      
      return;
    }
    
    // 添加到处理中的卡片，防止用户重复点击
    setProcessingCards(prev => new Set([...prev, cardId]));
    
    // 检测是否为移动设备
    const isMobile = window.innerWidth < 640;
    
    // 添加滚动逻辑，滚动到指定卡牌位置后再翻牌
    if (scrollRef.current) {
      // 找到对应卡牌的DOM元素
      const cardElements = scrollRef.current.querySelectorAll('.card-wrapper');
      const cardIndex = randomCardIndices.length === 78 
        ? randomCardIndices.findIndex(id => id === cardId) 
        : cardId;
      
      if (cardIndex >= 0 && cardIndex < cardElements.length) {
        const cardElement = cardElements[cardIndex];
        
        // 计算卡牌到视口左侧的距离
        const cardLeft = cardElement.getBoundingClientRect().left;
        const containerLeft = scrollRef.current.getBoundingClientRect().left;
        const offset = cardLeft - containerLeft;
        
        // 将卡牌滚动到容器中间位置（移动端靠左侧一些）
        const centerOffset = isMobile ? scrollRef.current.clientWidth / 3 : scrollRef.current.clientWidth / 2;
        const scrollTo = scrollRef.current.scrollLeft + offset - centerOffset + (cardElement.clientWidth / 2);
        
        // 滚动到卡牌位置
        scrollRef.current.scrollTo({
          left: scrollTo,
          behavior: 'smooth'
        });
        
        // 等待滚动完成后再翻牌 - 移动端减少延迟时间
        setTimeout(() => {
          // 随机确定卡牌正逆位（50%概率逆位）
          const isReversed = Math.random() < 0.5;
          
          // 批量更新状态
          setCardOrientations(prev => {
            const newOrientations = new Map(prev);
            newOrientations.set(cardId, isReversed);
            return newOrientations;
          });
          
          // 添加到已翻开的牌中
          setFlippedCards(prev => {
            // 再次检查是否超出限制
            if (prev.length >= requiredCards) return prev;
            return [...prev, cardId];
          });
          
          // 添加到选中的牌中
          setSelectedCards(prev => {
            // 再次检查是否超出限制
            if (prev.length >= requiredCards) return prev;
            return [...prev, cardId];
          });
          
          // 卡片动画完成后，从处理中的卡片移除
          setTimeout(() => {
            setProcessingCards(prev => {
              const newSet = new Set([...prev]);
              newSet.delete(cardId);
              return newSet;
            });
          }, ANIMATION_DURATION.resetProcessing);
        }, ANIMATION_DURATION.scrollDelay);
      } else {
        performCardFlip(cardId);
      }
    } else {
      performCardFlip(cardId);
    }
  };
  
  // 优化performCardFlip函数
  const performCardFlip = (cardId: number) => {
    // 随机确定卡牌正逆位（50%概率逆位）
    const isReversed = Math.random() < 0.5;
    
    // 批量更新状态，减少重渲染次数
    setCardOrientations(prev => {
      const newOrientations = new Map(prev);
      newOrientations.set(cardId, isReversed);
      return newOrientations;
    });
    
    // 添加到已翻开的牌中
    setFlippedCards(prev => {
      // 严格限制不超过所需数量
      if (prev.length >= requiredCards) return prev;
      return [...prev, cardId];
    });
    
    // 添加到选中的牌中
    setSelectedCards(prev => {
      // 严格限制不超过所需数量
      if (prev.length >= requiredCards) return prev;
      return [...prev, cardId];
    });
    
    // 卡片动画完成后，从处理中的卡片移除
    setTimeout(() => {
      setProcessingCards(prev => {
        const newSet = new Set([...prev]);
        newSet.delete(cardId);
        return newSet;
      });
    }, ANIMATION_DURATION.resetProcessing);
  };

  // 修改卡片点击事件处理
  const handleCardClick = (index: number) => {
    if (!dragActive && !flippedCards.includes(index)) {
      handleCardFlip(index);
    }
  };

  // 组成牌阵并前往结果页面
  const handleFormSpread = useCallback(async () => {
    // 检查用户权限（支持匿名用户）
    const hasPermission = await checkUserPermission();
    if (!hasPermission) {
      return;
    }

    // 对于登录用户，增强权限检查
    if (user && user.vipStatus !== 'active' && user.remainingReads <= 0) {
      setShowVipPrompt(true);
      return;
    }
    
    // 检查是否已选择足够的卡牌
    if (flippedCards.length < requiredCards) {
      // 清除之前的定时器
      if (floatingHintTimeoutRef.current) {
        clearTimeout(floatingHintTimeoutRef.current);
      }
      
      // 设置错误信息，提示需要选择更多卡牌
      setNumberInputError(t('reading.shuffle.error.not_enough_cards', { required: requiredCards }));
      
      // 2秒后隐藏提示
      floatingHintTimeoutRef.current = setTimeout(() => {
        setNumberInputError('');
      }, 2000);
      return;
    }

    // 标记卡片选择阶段已完成
    const cardsInfo = {
      selected: selectedCards,
      flipped: flippedCards,
      orientations: Array.from(cardOrientations.entries()),
      completed: true
    };
    localStorage.setItem('selectedCardsInfo', JSON.stringify(cardsInfo));

    // 准备选中的卡牌数据
    const selectedCardsData = flippedCards.map((cardId, index) => {
      const card = TAROT_CARDS[cardId];
      const isReversed = cardOrientations.get(cardId) || false;
      
      // 获取牌阵位置信息
      let position = '';
      if (translatedSpreadInfo && translatedSpreadInfo.positions && translatedSpreadInfo.positions[index]) {
        position = translatedSpreadInfo.positions[index];
      }
      
      return {
        id: cardId,
        name: card.name,
        nameEn: card.nameEn,
        isReversed: isReversed,
        orientation: isReversed ? 'reversed' : 'upright',
        position
      };
    });

    // 保存到localStorage
    localStorage.setItem('selectedCards', JSON.stringify(selectedCardsData));

    try {
      // 保存会话数据
      const sessionId = localStorage.getItem('sessionId');
      if (sessionId) {
        // 使用新的同步接口更新会话信息
        try {
          await axiosInstance.post('/api/reading/cards-selected', {
            selectedCards: selectedCardsData,
            sessionId: sessionId
          });
        } catch (error) {
          console.error('保存卡牌选择失败:', error);
          // 即使保存失败也继续流程，不阻塞用户操作
        }
        
      } else {
        // console.warn('无法更新会话信息：未找到会话ID');
        // 如果是年度运势牌阵，尝试创建会话
        if (spread && spread.id === 'yearly-fortune') {
          try {
            // 获取用户数据
            const userData = localStorage.getItem('yearlyFortuneData');
            if (userData) {
              const parsedUserData = JSON.parse(userData);
              // 创建会话
              const userQuestion = localStorage.getItem('userQuestion') || '请为我解读未来一年的运势';
              const sessionData: any = {
                question: userQuestion,
                status: 'cards_selected',
                selectedCards: selectedCardsData,
                selectedSpread: spread,
                userData: parsedUserData
              };

              // 如果是匿名用户，添加指纹信息
              if (!user) {
                try {
                  const fingerprint = await getBrowserFingerprint();
                  sessionData.fingerprint = fingerprint;
                } catch (error) {
                  console.error('获取浏览器指纹失败:', error);
                }
              }

              const response = await axiosInstance.post('/api/session', sessionData);
              
              if (response.data.success) {
                localStorage.setItem('sessionId', response.data.session.id);
              }
            }
          } catch (error) {
            // console.error('创建年度运势会话失败:', error);
          }
        }
      }
    } catch (error) {
      // console.error('更新会话信息失败:', error);
    }

    // 根据牌阵类型跳转到不同的结果页面
    if (spread && spread.id === 'yearly-fortune') {
      // 年运牌阵跳转到年运结果页面
      navigateRef.current('/yearly-fortune-result');
    } else {
      // 其他牌阵跳转到标准结果页面
      navigateRef.current('/tarot-result');
    }
  }, [flippedCards, requiredCards, selectedCards, cardOrientations, translatedSpreadInfo, spread, t, user]);

  // Scroll left and right functions for card deck
  const scroll = (direction: 'left' | 'right') => {
    if (scrollRef.current) {
      const { current: container } = scrollRef;
      const scrollAmount = direction === 'left' 
        ? -container.clientWidth // 改为滑动一整个视口宽度而不是半个
        : container.clientWidth;
      
      container.scrollBy({
        left: scrollAmount,
        behavior: 'smooth'
      });
    }
  };
  
  // 处理鼠标按下事件
  const handleMouseDown = (e: MouseEvent<HTMLDivElement>) => {
    if (!scrollRef.current) return;
    
    setIsDragging(true);
    setDragActive(false);
    setStartX(e.pageX - scrollRef.current.offsetLeft);
    setScrollLeft(scrollRef.current.scrollLeft);
    
    // 修改光标样式
    if (scrollRef.current) {
      scrollRef.current.style.cursor = 'grabbing';
      scrollRef.current.style.userSelect = 'none';
    }
  };
  
  // 处理鼠标移动事件
  const handleMouseMove = (e: MouseEvent<HTMLDivElement>) => {
    if (!isDragging || !scrollRef.current) return;
    
    // 防止选中文本
    e.preventDefault();
    
    const x = e.pageX - scrollRef.current.offsetLeft;
    const walk = (x - startX) * 2; // 滚动速度系数
    
    scrollRef.current.scrollLeft = scrollLeft - walk;
    
    // 如果拖动距离超过5像素，将dragActive设为true
    if (Math.abs(walk) > 5) {
      setDragActive(true);
    }
  };
  
  // 处理鼠标释放事件
  const handleMouseUp = () => {
    setIsDragging(false);
    
    // 恢复光标样式
    if (scrollRef.current) {
      scrollRef.current.style.cursor = 'grab';
      scrollRef.current.style.userSelect = 'auto';
    }
    
    // 短暂延迟后重置dragActive，以防止误触发卡片选择
    setTimeout(() => {
      setDragActive(false);
    }, 50);
  };
  
  // 处理鼠标离开事件
  const handleMouseLeave = () => {
    if (isDragging) {
      setIsDragging(false);
      
      // 恢复光标样式
      if (scrollRef.current) {
        scrollRef.current.style.cursor = 'grab';
        scrollRef.current.style.userSelect = 'auto';
      }
    }
  };

  // 修改数字选择处理函数
  const handleNumberSelect = () => {
    const number = parseInt(numberInputValue);
    if (isNaN(number) || number < 1 || number > 78) {
      if (numberInputErrorTimeoutRef.current) {
        clearTimeout(numberInputErrorTimeoutRef.current);
      }
      
      setNumberInputError(t('reading.shuffle.number_error.invalid_range'));
      
      numberInputErrorTimeoutRef.current = setTimeout(() => {
        setNumberInputError('');
      }, 2000);
      return;
    }
    
    // 数字输入是从1开始的，所以要减1得到索引
    const inputIndex = number - 1;
    
    // 直接使用输入的编号对应的位置，不需要再映射
    // 这里要判断是否有随机索引数组
    let cardId;
    if (randomCardIndices.length === 78) {
      // 使用随机索引数组中的第N张牌（用户输入的编号对应的牌）
      cardId = randomCardIndices[inputIndex];
    } else {
      // 如果没有随机索引数组，直接使用输入索引
      cardId = inputIndex;
    }
    
    if (flippedCards.includes(cardId)) {
      if (numberInputErrorTimeoutRef.current) {
        clearTimeout(numberInputErrorTimeoutRef.current);
      }
      
      setNumberInputError(t('reading.shuffle.number_error.already_selected'));
      
      numberInputErrorTimeoutRef.current = setTimeout(() => {
        setNumberInputError('');
      }, 2000);
      return;
    }
    
    if (flippedCards.length >= requiredCards) {
      if (numberInputErrorTimeoutRef.current) {
        clearTimeout(numberInputErrorTimeoutRef.current);
      }
      
      setNumberInputError(t('reading.shuffle.number_error.enough_cards'));
      
      numberInputErrorTimeoutRef.current = setTimeout(() => {
        setNumberInputError('');
      }, 2000);
      return;
    }
    
    setNumberInputValue('');
    handleCardFlip(cardId);
  };

  // 处理自定义选牌
  const handleCustomCardSelect = async (cardId: number) => {
    // 检查用户权限
    const hasPermission = await checkUserPermission();
    if (!hasPermission) return;
    
    if (selectedPosition === null) return;
    
    // 如果这个位置已经有牌了，先移除
    if (flippedCards.includes(cardId)) {
      setFlippedCards(prev => prev.filter(id => id !== cardId));
      setSelectedCards(prev => prev.filter(id => id !== cardId));
    }
    
    // 随机确定卡牌正逆位（50%概率逆位）
    const isReversed = Math.random() < 0.5;
    setCardOrientations(prev => {
      const newOrientations = new Map(prev);
      newOrientations.set(cardId, isReversed);
      return newOrientations;
    });
    
    // 更新已翻开的牌
    setFlippedCards(prev => {
      const newFlipped = [...prev];
      newFlipped[selectedPosition] = cardId;
      return newFlipped;
    });
    
    // 更新选中的牌
    setSelectedCards(prev => {
      const newSelected = [...prev];
      newSelected[selectedPosition] = cardId;
      return newSelected;
    });
    
    setSelectedPosition(null);
  };

  return (
    <div className="min-h-screen flex flex-col relative">
      <SEO 
        title={t('reading.seo.shuffle_title', '塔罗牌洗牌和抽牌')} 
        description={t('reading.seo.shuffle_description', '选择您的塔罗牌并进行洗牌，以获取对您问题的准确解读和指导。')}
        spreadName={translatedSpreadInfo?.name}
        spreadDescription={translatedSpreadInfo?.description}
      />
      <LandingBackground />
      
      {/* 浮动提示 */}
      <FloatingHint message={numberInputError} isDark={isDark} />
      
      {/* Main content */}
      <div className="flex-grow relative z-10">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8 pt-0 sm:pt-1 pb-8">
          <div className="flex flex-col gap-4">
            {/* Card deck with horizontal scrolling */}
            <motion.div 
              className="relative"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ duration: 0.5, delay: 0.3 }}
            >
              <div className="text-center mt-6 sm:mt-8 md:mt-10">
                <h2 className="main-title mb-2 sm:mb-3">{t('reading.shuffle.select_prompt')}</h2>
                <p className="sub-title mb-4 sm:mb-6">{t('reader.subtitle')}</p>
              </div>
              
              {/* 选牌模式切换 */}
              <SelectionModeToggle 
                selectionMode={selectionMode}
                setSelectionMode={setSelectionMode}
                isDark={isDark}
                t={t}
              />
              
              {/* 滑动浏览组件 - 根据模式显示 */}
              {selectionMode === 'slide' && (
                <SlideControls 
                  onScroll={scroll}
                  isDark={isDark}
                  t={t}
                />
              )}

              {/* 数字选牌组件 - 根据模式显示 */}
              {selectionMode === 'number' && (
                <NumberInput 
                  value={numberInputValue}
                  onChange={setNumberInputValue}
                  onSubmit={handleNumberSelect}
                  theme={theme}
                  t={t}
                />
              )}
              
              {/* 卡牌选择区域 - 根据模式显示 */}
              {selectionMode !== 'custom' && (
                <CardDeck 
                  scrollRef={scrollRef}
                  isDark={isDark}
                  randomCardIndices={randomCardIndices}
                  flippedCards={flippedCards}
                  processingCards={processingCards}
                  cardOrientations={cardOrientations}
                  cardBackImage={cardBackImage}
                  cardImages={cardImages}
                  currentLanguage={currentLanguage}
                  dragActive={dragActive}
                  animationDuration={ANIMATION_DURATION}
                  handleMouseDown={handleMouseDown}
                  handleMouseMove={handleMouseMove}
                  handleMouseUp={handleMouseUp}
                  handleMouseLeave={handleMouseLeave}
                  handleCardClick={handleCardClick}
                  t={t}
                />
              )}
            </motion.div>

            {/* Action button */}
            {selectionMode !== 'custom' && (
              <ActionButton 
                onClick={handleFormSpread}
                isEnabled={flippedCards.length === requiredCards}
                isDark={isDark}
                t={t}
                requiredCards={requiredCards}
                selectedCount={flippedCards.length}
              />
            )}

            {/* Card positions at the bottom */}
            <SpreadContainer 
              isDark={isDark}
              t={t}
              translatedSpreadInfo={translatedSpreadInfo}
              requiredCards={requiredCards}
              flippedCards={flippedCards}
              selectedCards={selectedCards}
              cardOrientations={cardOrientations}
              cardImages={cardImages}
              currentLanguage={currentLanguage}
              selectionMode={selectionMode}
              handleFormSpread={handleFormSpread}
              onSelectPosition={(index) => {
                setSelectedPosition(index);
                setShowCardSelector(true);
              }}
              onToggleOrientation={(cardId) => {
                setCardOrientations(prev => {
                  const newOrientations = new Map(prev);
                  newOrientations.set(cardId, !prev.get(cardId));
                  return newOrientations;
                });
              }}
            />

            {/* 添加CSS样式 */}
            <TarotCardSelectionStyles isDark={isDark} />
          </div>
        </div>
      </div>
      
      {/* 添加卡牌选择器底部弹出层 */}
      <CardSelectionBottomSheet
        isOpen={showCardSelector}
        onClose={() => {
          setShowCardSelector(false);
          setSelectedPosition(null);
        }}
        onSelectCard={handleCustomCardSelect}
        cardImages={cardImages}
      />
      
      {/* 添加VIP提示对话框 */}
      <VipPromptDialog 
        isOpen={showVipPrompt} 
        onCancel={() => setShowVipPrompt(false)}
      />

      {/* 添加登录提示对话框 */}
      <LoginPrompt 
        isOpen={showLoginPrompt} 
        onClose={() => setShowLoginPrompt(false)}
      />

      {/* 添加页脚导航 */}
      <div className="relative z-10">
        <Footer />
      </div>
    </div>
  );
};

export default TarotCardSelection; 