/**
 * IP地址获取工具函数
 * 用于从Express请求对象中正确获取客户端真实IP地址
 */

/**
 * 获取客户端真实IP地址
 * @param {Object} req - Express请求对象
 * @returns {string} 客户端IP地址
 */
function getClientIP(req) {
  // 优先级顺序：
  // 1. X-Forwarded-For 头（代理服务器设置的真实客户端IP）
  // 2. X-Real-IP 头（nginx等代理服务器设置）
  // 3. req.ip（Express在trust proxy模式下解析的IP）
  // 4. req.connection.remoteAddress（直连情况下的IP）
  // 5. req.socket.remoteAddress（备用获取方式）
  
  let ip = null;
  
  // 1. 检查 X-Forwarded-For 头
  const xForwardedFor = req.headers['x-forwarded-for'];
  if (xForwardedFor) {
    // X-Forwarded-For 可能包含多个IP，第一个是真实客户端IP
    ip = xForwardedFor.split(',')[0].trim();
  }
  
  // 2. 检查 X-Real-IP 头
  if (!ip) {
    ip = req.headers['x-real-ip'];
  }
  
  // 3. 使用 Express 的 req.ip（需要设置 trust proxy）
  if (!ip) {
    ip = req.ip;
  }
  
  // 4. 备用方案：直接从连接获取
  if (!ip) {
    ip = req.connection?.remoteAddress || req.socket?.remoteAddress;
  }
  
  // 处理IPv6映射的IPv4地址
  if (ip && ip.startsWith('::ffff:')) {
    ip = ip.substring(7); // 移除 '::ffff:' 前缀
  }
  
  // 如果仍然是本地地址，在开发环境下可能是正常的
  // 不再输出调试信息
  
  return ip || 'unknown';
}

/**
 * 验证IP地址格式是否有效
 * @param {string} ip - IP地址字符串
 * @returns {boolean} 是否为有效IP地址
 */
function isValidIP(ip) {
  if (!ip || ip === 'unknown') return false;
  
  // IPv4 正则表达式
  const ipv4Regex = /^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$/;
  
  // IPv6 正则表达式（简化版）
  const ipv6Regex = /^(?:[0-9a-fA-F]{1,4}:){7}[0-9a-fA-F]{1,4}$|^::1$|^::$/;
  
  return ipv4Regex.test(ip) || ipv6Regex.test(ip);
}

/**
 * 获取IP地址的地理位置信息
 * @param {string} ip - IP地址
 * @returns {Object|null} 地理位置信息或null
 */
function getIPGeolocation(ip) {
  try {
    const geoip = require('geoip-lite');
    
    // 跳过本地IP地址
    if (ip === '127.0.0.1' || ip === '::1' || ip === 'unknown') {
      return null;
    }
    
    return geoip.lookup(ip);
  } catch (error) {
    console.error('获取IP地理位置信息失败:', error);
    return null;
  }
}

/**
 * 记录IP获取的调试信息
 * @param {Object} req - Express请求对象
 * @param {string} context - 调用上下文（用于日志标识）
 */
function logIPDebugInfo(req, context = 'unknown') {
  const debugInfo = {
    context,
    headers: {
      'x-forwarded-for': req.headers['x-forwarded-for'],
      'x-real-ip': req.headers['x-real-ip'],
      'user-agent': req.headers['user-agent']
    },
    express: {
      ip: req.ip,
      ips: req.ips
    },
    connection: {
      remoteAddress: req.connection?.remoteAddress,
      socketRemoteAddress: req.socket?.remoteAddress
    },
    finalIP: getClientIP(req)
  };
  
  console.log(`IP调试信息 [${context}]:`, JSON.stringify(debugInfo, null, 2));
}

module.exports = {
  getClientIP,
  isValidIP,
  getIPGeolocation,
  logIPDebugInfo
};
