import React, { createContext, useContext, useState, useEffect } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { User, getCurrentUser } from '../services/userService';
import { authEvents, AUTH_ERROR_EVENT } from '../utils/axios';
import { getBrowserFingerprint } from '../utils/fingerprint';
import { checkAnonymousEligibility } from '../services/anonymousService';

// 扩展用户类型以支持匿名用户状态
interface AnonymousUserState {
  isAnonymous: true;
  remainingReads: number;
  hasUsedFreeRead: boolean;
  fingerprint?: string;
}

interface UserContextType {
  user: User | null;
  anonymousState: AnonymousUserState | null;
  setUser: React.Dispatch<React.SetStateAction<User | null>>;
  loading: boolean;
  logout: () => void;
  refreshUser: () => Promise<User | void>;
  refreshAnonymousState: () => Promise<void>;
}

const UserContext = createContext<UserContextType | undefined>(undefined);

export const UserProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const cachedUser = localStorage.getItem('cachedUser');
  const cachedAnonymousState = localStorage.getItem('cachedAnonymousState');

  const [user, setUser] = useState<User | null>(cachedUser ? JSON.parse(cachedUser) : null);
  const [anonymousState, setAnonymousState] = useState<AnonymousUserState | null>(
    cachedAnonymousState ? JSON.parse(cachedAnonymousState) : null
  );
  const [loading, setLoading] = useState(true);
  const navigate = useNavigate();
  const location = useLocation();

  // Combined effect for user initialization and caching
  useEffect(() => {
    const initializeUser = async () => {
      const token = localStorage.getItem('token');
      if (token) {
        // 登录用户逻辑
        try {
          const userData = await getCurrentUser();
          setUser(userData);
          localStorage.setItem('userId', userData.id);
          localStorage.setItem('cachedUser', JSON.stringify(userData));
          // 清除匿名用户状态
          setAnonymousState(null);
          localStorage.removeItem('cachedAnonymousState');
        } catch (error) {
          // console.error('Failed to get user data:', error);
          localStorage.removeItem('token');
          localStorage.removeItem('userId');
          localStorage.removeItem('cachedUser');
          setUser(null);
          // 初始化匿名用户状态
          await initializeAnonymousState();
        }
      } else {
        // 未登录用户逻辑
        localStorage.removeItem('cachedUser');
        setUser(null);
        await initializeAnonymousState();
      }
      setLoading(false);
    };

    const initializeAnonymousState = async () => {
      try {
        // 首先检查是否有缓存的匿名状态
        const cachedState = localStorage.getItem('cachedAnonymousState');
        if (cachedState) {
          try {
            const parsedState = JSON.parse(cachedState);
            // 验证缓存状态的有效性
            if (parsedState && typeof parsedState.isAnonymous === 'boolean') {
              setAnonymousState(parsedState);
              return; // 使用缓存状态，不需要重新获取指纹
            }
          } catch (e) {
            // 缓存解析失败，继续获取新状态
          }
        }

        // 没有有效缓存，获取新的指纹和状态
        const fingerprint = await getBrowserFingerprint();
        const eligibility = await checkAnonymousEligibility(fingerprint);

        const newAnonymousState: AnonymousUserState = {
          isAnonymous: true,
          remainingReads: eligibility.hasUsed ? 0 : 1,
          hasUsedFreeRead: eligibility.hasUsed,
          fingerprint
        };

        setAnonymousState(newAnonymousState);
        localStorage.setItem('cachedAnonymousState', JSON.stringify(newAnonymousState));
      } catch (error) {
        console.error('初始化匿名用户状态失败:', error);
        // 设置默认状态
        const defaultAnonymousState: AnonymousUserState = {
          isAnonymous: true,
          remainingReads: 0,
          hasUsedFreeRead: true
        };
        setAnonymousState(defaultAnonymousState);
        localStorage.setItem('cachedAnonymousState', JSON.stringify(defaultAnonymousState));
      }
    };

    initializeUser();
  }, []); // Only run on mount

  // 监听用户状态变化
  useEffect(() => {
    if (user) {
      localStorage.setItem('cachedUser', JSON.stringify(user));
    }
  }, [user]);

  // 监听匿名用户状态变化
  useEffect(() => {
    if (anonymousState) {
      localStorage.setItem('cachedAnonymousState', JSON.stringify(anonymousState));
    }
  }, [anonymousState]);



  useEffect(() => {
    const handleAuthError = () => {
      // 清除用户认证信息，但不自动重定向
      localStorage.removeItem('token');
      localStorage.removeItem('userId');
      localStorage.removeItem('cachedUser');
      setUser(null);
      // 删除自动重定向到登录页面的代码
    };

    // 监听未授权事件
    authEvents.addEventListener(AUTH_ERROR_EVENT, handleAuthError);

    return () => {
      authEvents.removeEventListener(AUTH_ERROR_EVENT, handleAuthError);
    };
  }, []);

  const logout = async () => {
    localStorage.removeItem('token');
    localStorage.removeItem('userId');
    localStorage.removeItem('cachedUser');
    setUser(null);

    // 重新初始化匿名用户状态
    try {
      const fingerprint = await getBrowserFingerprint();
      const eligibility = await checkAnonymousEligibility(fingerprint);

      const newAnonymousState: AnonymousUserState = {
        isAnonymous: true,
        remainingReads: eligibility.hasUsed ? 0 : 1,
        hasUsedFreeRead: eligibility.hasUsed,
        fingerprint
      };

      setAnonymousState(newAnonymousState);
      localStorage.setItem('cachedAnonymousState', JSON.stringify(newAnonymousState));
    } catch (error) {
      console.error('登出后初始化匿名状态失败:', error);
    }

    navigate('/login');
  };
  
  // 刷新用户信息
  const refreshUser = async () => {
    const token = localStorage.getItem('token');
    if (token) {
      try {
        const userData = await getCurrentUser();
        setUser(userData);
        localStorage.setItem('userId', userData.id);
        localStorage.setItem('cachedUser', JSON.stringify(userData));
        return userData;
      } catch (error) {
        console.error('刷新用户信息失败:', error);
        throw error;
      }
    } else {
      throw new Error('用户未登录');
    }
  };

  // 刷新匿名用户状态
  const refreshAnonymousState = async () => {
    try {
      const fingerprint = await getBrowserFingerprint();
      const eligibility = await checkAnonymousEligibility(fingerprint);

      const newAnonymousState: AnonymousUserState = {
        isAnonymous: true,
        remainingReads: eligibility.hasUsed ? 0 : 1,
        hasUsedFreeRead: eligibility.hasUsed,
        fingerprint
      };

      setAnonymousState(newAnonymousState);
      localStorage.setItem('cachedAnonymousState', JSON.stringify(newAnonymousState));
    } catch (error) {
      console.error('刷新匿名用户状态失败:', error);
      throw error;
    }
  };

  // 监听路由变化，在关键页面自动刷新匿名用户状态（复用登录用户的机制）
  useEffect(() => {
    const shouldRefreshAnonymousState = () => {
      // 只在未登录状态下刷新匿名状态
      if (user) return false;

      // 在这些页面自动刷新匿名状态，类似登录用户的机制
      const refreshPages = ['/home', '/yes-no-tarot', '/ask-question'];
      return refreshPages.some(page => location.pathname.includes(page));
    };

    if (shouldRefreshAnonymousState()) {
      // 异步刷新匿名状态，不阻塞页面渲染
      const refreshAsync = async () => {
        try {
          await refreshAnonymousState();
        } catch (error) {
          console.error('路由变化时刷新匿名状态失败:', error);
        }
      };
      refreshAsync();
    }
  }, [location.pathname, user]);

  return (
    <UserContext.Provider value={{
      user,
      anonymousState,
      setUser,
      loading,
      logout,
      refreshUser,
      refreshAnonymousState
    }}>
      {children}
    </UserContext.Provider>
  );
};

export const useUser = () => {
  const context = useContext(UserContext);
  if (context === undefined) {
    throw new Error('useUser must be used within a UserProvider');
  }
  return context;
};
