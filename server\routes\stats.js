const express = require('express');
const router = express.Router();
const StatsService = require('../services/statsService');
const { authenticateToken } = require('../middleware/auth');
const { v4: uuidv4 } = require('uuid');
const { getConnection } = require('../services/database');
const { getClientIP } = require('../utils/ipUtils');

// 更新用户统计数据（每天自动运行一次）
router.post('/users/update', authenticateToken, async (req, res) => {
  try {
    const stats = await StatsService.updateUserStats();
    res.json({ success: true, stats });
  } catch (error) {
    console.error('Error updating user stats:', error);
    res.status(500).json({ success: false, message: '更新用户统计数据失败' });
  }
});

// 获取用户统计数据
router.get('/users', authenticateToken, async (req, res) => {
  try {
    const { period = '30d' } = req.query;
    const stats = await StatsService.getUserStats(period);
    res.json({ success: true, stats });
  } catch (error) {
    console.error('Error getting user stats:', error);
    res.status(500).json({ success: false, message: '获取用户统计数据失败' });
  }
});

// 获取API调用统计数据
router.get('/api', authenticateToken, async (req, res) => {
  try {
    const { period = '24h' } = req.query;
    const stats = await StatsService.getApiStats(period);
    res.json({ success: true, stats });
  } catch (error) {
    console.error('Error getting API stats:', error);
    res.status(500).json({ success: false, message: '获取API统计数据失败' });
  }
});

// 处理可能过长的路径
const sanitizePath = (path) => {
  if (!path) return '';
  // 如果路径过长，截取前200个字符
  if (path.length > 2000) {
    console.warn(`Path is too long (${path.length} chars), truncating: ${path.substring(0, 50)}...`);
    return path.substring(0, 2000);
  }
  return path;
};

// 记录页面访问
router.post('/pageview', async (req, res) => {
  try {
    const {
      userId,      // 可选，已登录用户的ID
      sessionId,   // 前端生成的会话ID
      pagePath,    // 访问的页面路径
      referrer     // 来源页面
    } = req.body;

    // 验证必需的session_id
    if (!sessionId) {
      return res.status(400).json({
        success: false,
        message: 'session_id不能为空'
      });
    }

    // 获取客户端IP
    const ip = getClientIP(req);

    // 获取User-Agent
    const userAgent = req.headers['user-agent'];

    // 处理可能过长的路径
    const sanitizedPagePath = sanitizePath(pagePath);
    const sanitizedReferrer = sanitizePath(referrer);

    const pool = await getConnection();
    
    // 检查用户ID是否存在
    let finalUserId = null;
    if (userId) {
      const [userRows] = await pool.query('SELECT id FROM users WHERE id = ?', [userId]);
      if (userRows && userRows.length > 0) {
        finalUserId = userId;
      } else {
        console.warn(`User ID ${userId} not found in users table, setting to null`);
      }
    }

    await pool.query(
      `INSERT INTO page_views (
        id, user_id, session_id, ip_address, 
        user_agent, page_path, referrer
      ) VALUES (?, ?, ?, ?, ?, ?, ?)`,
      [
        uuidv4(),
        finalUserId,
        sessionId,
        ip,
        userAgent,
        sanitizedPagePath,
        sanitizedReferrer || null
      ]
    );

    res.json({ success: true });
  } catch (error) {
    console.error('Error recording page view:', error);
    res.status(500).json({ 
      success: false, 
      message: 'Failed to record page view' 
    });
  }
});

module.exports = router; 