// import FingerprintJS from '@fingerprintjs/fingerprintjs';
import FingerprintJS from '@fingerprintjs/fingerprintjs';

// 创建指纹实例
let fpPromise: Promise<any> | null = null;

// 全局指纹生成状态，防止重复生成
let fingerprintGenerationPromise: Promise<string> | null = null;

/**
 * 获取浏览器指纹 - 已启用，并优化长度
 * @returns 返回优化后的浏览器指纹
 */
// 指纹生成报告接口
interface FingerprintReport {
  fingerprint: string;
  components: {
    visitorId: {
      success: boolean;
      value: string;
      error?: string;
    };
    canvas: {
      success: boolean;
      value: string;
      usedRandom: boolean;
      error?: string;
    };
    userAgent: {
      success: boolean;
      value: string;
      usedRandom: boolean;
      error?: string;
    };
    screenResolution: {
      success: boolean;
      value: string;
      usedRandom: boolean;
      usedWindowScreen: boolean;
      error?: string;
    };
  };
}

export const getBrowserFingerprint = async (): Promise<string> => {
  // 首先检查sessionStorage中是否有缓存的指纹
  try {
    const savedFingerprint = sessionStorage.getItem('browserFingerprint');
    if (savedFingerprint) {
      return savedFingerprint;
    }
  } catch (e) {
    // 处理无法从sessionStorage获取指纹的情况，继续生成新指纹
  }

  // 如果已经有正在进行的指纹生成，等待其完成
  if (fingerprintGenerationPromise) {
    return fingerprintGenerationPromise;
  }

  // 创建新的指纹生成Promise
  fingerprintGenerationPromise = (async () => {
    try {
    if (!fpPromise) {
      // 初始化指纹实例
      fpPromise = FingerprintJS.load().catch(err => {
        throw err;
      });
    }

    // 添加超时处理，确保不会无限等待
    const timeoutPromise = new Promise<never>((_, reject) => {
      setTimeout(() => reject(new Error('获取指纹超时(5秒)')), 5000);
    });

    // 使用Promise.race确保请求不会无限等待
    const fp = await Promise.race([fpPromise, timeoutPromise]);

    // 获取指纹结果
    const result = await fp.get({ extendedResult: true });

    if (!result || !result.visitorId) {
      throw new Error('指纹生成失败: 没有获取到有效的visitorId');
    }

    // 创建生成报告
    const report: FingerprintReport = {
      fingerprint: '',
      components: {
        visitorId: {
          success: true,
          value: result.visitorId
        },
        canvas: {
          success: false,
          value: '',
          usedRandom: false
        },
        userAgent: {
          success: false,
          value: '',
          usedRandom: false
        },
        screenResolution: {
          success: false,
          value: '',
          usedRandom: false,
          usedWindowScreen: false
        }
      }
    };



    // 从canvas值创建哈希值而不是使用完整字符串
    let canvasHash = '';
    try {
      if (result.components?.canvas?.value) {
        const canvasValue = result.components.canvas.value;
        // 简化canvas指纹：创建一个简单的哈希
        const canvasString = typeof canvasValue === 'object' ?
          JSON.stringify(canvasValue).substring(0, 50) : // 仅取前50个字符
          String(canvasValue).substring(0, 50);

        // 简单的哈希函数
        let hash = 0;
        for (let i = 0; i < canvasString.length; i++) {
          const char = canvasString.charCodeAt(i);
          hash = ((hash << 5) - hash) + char;
          hash = hash & hash; // 转换为32位整数
        }
        canvasHash = Math.abs(hash).toString(36); // 转为较短的base36字符串

        // 记录成功获取
        report.components.canvas.success = true;
        report.components.canvas.value = canvasHash;
        report.components.canvas.usedRandom = false;
      }
      // 如果没有获取到canvas，使用随机值
      if (!canvasHash) {
        canvasHash = 'c' + Math.random().toString(36).substring(2, 8);
        report.components.canvas.success = false;
        report.components.canvas.value = canvasHash;
        report.components.canvas.usedRandom = true;
        report.components.canvas.error = 'Canvas组件不可用或为空';
      }
    } catch (e) {
      canvasHash = 'c-err';
      report.components.canvas.success = false;
      report.components.canvas.value = canvasHash;
      report.components.canvas.usedRandom = false;
      report.components.canvas.error = `Canvas处理异常: ${e instanceof Error ? e.message : String(e)}`;
    }
    
    // 简化屏幕分辨率格式
    let resolution = '';
    try {
      if (result.components?.screenResolution?.value) {
        if (Array.isArray(result.components.screenResolution.value)) {
          resolution = result.components.screenResolution.value.join('x');
        } else {
          resolution = String(result.components.screenResolution.value).replace(/\s+/g, '');
        }

        // 记录成功从FingerprintJS获取
        report.components.screenResolution.success = true;
        report.components.screenResolution.value = resolution;
        report.components.screenResolution.usedRandom = false;
        report.components.screenResolution.usedWindowScreen = false;
      } else {
        // 尝试直接从window.screen获取
        if (window.screen && window.screen.width && window.screen.height) {
          resolution = `${window.screen.width}x${window.screen.height}`;
          report.components.screenResolution.success = true;
          report.components.screenResolution.value = resolution;
          report.components.screenResolution.usedRandom = false;
          report.components.screenResolution.usedWindowScreen = true;
          report.components.screenResolution.error = 'FingerprintJS未提供screenResolution，使用window.screen';
        }
      }

      // 如果没有获取到分辨率，使用随机值
      if (!resolution) {
        // 生成一个随机的分辨率值，确保每次都不同
        const randomWidth = 1920 + Math.floor(Math.random() * 1000); // 1920-2919
        const randomHeight = 1080 + Math.floor(Math.random() * 500); // 1080-1579
        resolution = `${randomWidth}x${randomHeight}`;
        report.components.screenResolution.success = false;
        report.components.screenResolution.value = resolution;
        report.components.screenResolution.usedRandom = true;
        report.components.screenResolution.usedWindowScreen = false;
        report.components.screenResolution.error = 'FingerprintJS和window.screen都无法获取分辨率';
      }
    } catch (e) {
      resolution = 'r-err';
      report.components.screenResolution.success = false;
      report.components.screenResolution.value = resolution;
      report.components.screenResolution.usedRandom = false;
      report.components.screenResolution.usedWindowScreen = false;
      report.components.screenResolution.error = `屏幕分辨率处理异常: ${e instanceof Error ? e.message : String(e)}`;
    }
    
    // 用户代理哈希
    let uaHash = '';
    try {
      const ua = navigator.userAgent || '';
      if (ua) {
        let hash = 0;
        for (let i = 0; i < ua.length; i++) {
          const char = ua.charCodeAt(i);
          hash = ((hash << 5) - hash) + char;
          hash = hash & hash;
        }
        uaHash = Math.abs(hash).toString(36);

        // 记录成功获取
        report.components.userAgent.success = true;
        report.components.userAgent.value = uaHash;
        report.components.userAgent.usedRandom = false;
      }
      // 如果没有获取到userAgent，使用随机值
      if (!uaHash) {
        uaHash = 'ua' + Math.random().toString(36).substring(2, 8);
        report.components.userAgent.success = false;
        report.components.userAgent.value = uaHash;
        report.components.userAgent.usedRandom = true;
        report.components.userAgent.error = 'navigator.userAgent为空或不可用';
      }
    } catch (e) {
      uaHash = 'ua-err';
      report.components.userAgent.success = false;
      report.components.userAgent.value = uaHash;
      report.components.userAgent.usedRandom = false;
      report.components.userAgent.error = `UserAgent处理异常: ${e instanceof Error ? e.message : String(e)}`;
    }
    
    // 优化后的指纹：更短但仍保持唯一性
    const optimizedFingerprint = [
      result.visitorId,     // 保留原始visitorId（这是最关键的部分）
      canvasHash,           // Canvas哈希（大大缩短）
      uaHash,               // UserAgent哈希（缩短）
      resolution            // 屏幕分辨率（格式简化）
    ].join('-');

    // 完善报告
    report.fingerprint = optimizedFingerprint;

    // 发送报告到后端（异步，不阻塞指纹返回）
    try {
      // 使用fetch发送报告，避免依赖axios
      fetch('/api/api/fingerprint-report', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(report)
      }).catch(err => {
        // 静默处理错误，不影响指纹生成
        console.error('发送指纹报告失败:', err);
      });
    } catch (e) {
      // 静默处理错误
    }

    // 保存到sessionStorage以便同一会话中重复使用
    try {
      sessionStorage.setItem('browserFingerprint', optimizedFingerprint);
    } catch (e) {
      // 处理无法保存到sessionStorage的情况
    }
      return optimizedFingerprint;
    } catch (error) {
      // 尝试从sessionStorage获取之前保存的指纹
      try {
        const savedFingerprint = sessionStorage.getItem('browserFingerprint');
        if (savedFingerprint) {
          return savedFingerprint;
        }
      } catch (e) {
        // 处理无法从sessionStorage获取指纹的情况
      }

      // 如果获取失败，返回一个基于当前时间的随机值
      const fallbackId = `fallback-${Date.now()}-${Math.random().toString(36).substring(2, 15)}`;
      return fallbackId;
    } finally {
      // 清除生成状态，允许下次重新生成
      fingerprintGenerationPromise = null;
    }
  })();

  return fingerprintGenerationPromise;
};