const express = require('express');
const router = express.Router();
const axios = require('axios');
const { User } = require('../models/User');
const { authenticateToken, optionalAuthenticateToken } = require('../middleware/auth');
const { TAROT_CARDS } = require('../constants/tarot');
const { getClientIP } = require('../utils/ipUtils');

// 创建用于调用 Deepseek API 的 axios 实例
const deepseekAPI = axios.create({
  baseURL: 'https://api.deepseek.com',
  timeout: 60000,
  headers: {
    'Authorization': `Bearer ${process.env.DEEPSEEK_API_KEY}`,
    'Content-Type': 'application/json'
  }
});

router.post('/', optionalAuthenticateToken, async (req, res) => {
  try {
    const { question, selectedReader, selectedSpread, selectedCards, fingerprint } = req.body;

    // 处理匿名用户
    if (!req.user) {
      if (!fingerprint) {
        return res.status(400).json({ error: '匿名用户需要提供浏览器指纹' });
      }

      // 检查匿名用户是否已使用过免费机会
      const { getConnection } = require('../services/database');
      const pool = await getConnection();
      const [existingRecords] = await pool.query(
        'SELECT COUNT(*) as count FROM anonymous_divination_records WHERE browser_fingerprint = ?',
        [fingerprint]
      );

      if (existingRecords[0].count > 0) {
        return res.status(403).json({
          error: '您的免费占卜次数已用完，请登录获取更多次数',
          errorCode: 'ANONYMOUS_LIMIT_EXCEEDED'
        });
      }

      // 匿名用户可以继续，使用默认设置
      console.log('Anonymous user tarot reading request');
    } else {
      // 登录用户逻辑
      const userId = req.user.userId;
      console.log('User ID:', userId);

      // 获取用户信息
      const user = await User.findById(userId);
      if (!user) {
        console.error('User not found:', userId);
        return res.status(404).json({ error: '用户不存在' });
      }

      console.log('Found user:', user);
    }

    // 继续处理占卜逻辑（对登录用户和匿名用户都适用）

    // 获取占卜师风格和牌阵信息
    const readerStyle = selectedReader?.style || '温和细腻';
    const spread = selectedSpread || {};
    
    // 将卡牌信息转换为详细描述
    const cardsDescription = selectedCards.map((card, index) => {
      const position = spread.positions?.[index]?.name || `位置${index + 1}`;
      const isReversed = card.isReversed;
      const cardData = TAROT_CARDS.find(c => c.name === card.name);
      if (!cardData) {
        throw new Error(`找不到卡牌数据：${card.name}`);
      }
      
      const meaning = isReversed ? cardData.reversedMeaning : cardData.uprightMeaning;
      if (!meaning) {
        throw new Error(`卡牌 ${card.name} 缺少${isReversed ? '逆位' : '正位'}含义`);
      }
      
      // 获取更详细的含义，添加空值检查
      const detailedMeaning = {
        general: Array.isArray(meaning.general) ? meaning.general : ['暂无解释'],
        love: meaning.love || '暂无解释',
        career: meaning.career || '暂无解释',
        wealth: meaning.wealth || '暂无解释',
        advice: meaning.advice || '暂无建议'
      };

      return `
${position}：${card.name} (${isReversed ? '逆位' : '正位'})
- 牌面描述：${cardData.description || '暂无描述'}
- ${isReversed ? '逆位' : '正位'}含义：
  • 总体含义：${detailedMeaning.general.join('；')}
  • 感情：${detailedMeaning.love}
  • 事业：${detailedMeaning.career}
  • 财富：${detailedMeaning.wealth}
  • 建议：${detailedMeaning.advice}`;
    }).join('\n\n');

    // 构建占卜结果
    const readingResult = {
      question,
      reader: selectedReader,
      spread: selectedSpread,
      cards: selectedCards,
      cardsDescription,
      timestamp: new Date().toISOString()
    };

    // 如果是匿名用户，记录到匿名占卜表
    if (!req.user && fingerprint) {
      const { getConnection } = require('../services/database');
      const { v4: uuidv4 } = require('uuid');

      try {
        const pool = await getConnection();
        const recordId = uuidv4();
        const sessionId = req.body.sessionId || uuidv4();

        await pool.query(
          `INSERT INTO anonymous_divination_records
           (id, browser_fingerprint, session_id, question, spread_id, spread_name,
            selected_cards, reading_result, ip_address, input_tokens, output_tokens)
           VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`,
          [
            recordId,
            fingerprint,
            sessionId,
            question,
            selectedSpread?.id || null,
            selectedSpread?.name || null,
            JSON.stringify(selectedCards),
            JSON.stringify(readingResult),
            getClientIP(req),
            0, // 基础塔罗占卜不使用AI，tokens为0
            0
          ]
        );

        console.log('匿名占卜记录已保存:', recordId);
      } catch (dbError) {
        console.error('保存匿名占卜记录失败:', dbError);
        // 不阻止占卜继续进行
      }
    }

    // 返回占卜结果
    res.json({
      success: true,
      reading: readingResult,
      message: '占卜完成'
    });

  } catch (error) {
    console.error('Error in tarot reading:', error);
    res.status(500).json({ error: error.message });
  }
});

// 获取占卜历史
router.get('/history', authenticateToken, async (req, res) => {
  try {
    const { page = 1, limit = 10 } = req.query;
    const history = await tarotService.getUserSessions(req.user.userId, parseInt(page), parseInt(limit));
    res.json(history);
  } catch (error) {
    console.error('Error getting session history:', error);
    res.status(500).json({ message: 'Error getting session history' });
  }
});

module.exports = router;
