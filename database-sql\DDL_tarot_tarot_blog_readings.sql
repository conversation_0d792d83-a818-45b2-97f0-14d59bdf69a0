/******************************************/
/*   DatabaseName = tarot   */
/*   TableName = blog_readings   */
/******************************************/
CREATE TABLE `blog_readings` (
  `id` int NOT NULL AUTO_INCREMENT,
  `fingerprint` varchar(255) NOT NULL,
  `blog_id` varchar(255) NOT NULL,
  `question` text,
  `prompt_content` text,
  `response_content` text,
  `spread_type` varchar(100) DEFAULT NULL COMMENT '牌阵类型',
  `selected_cards` text COMMENT '抽出的卡牌，JSON格式',
  `status` varchar(50) DEFAULT 'completed' COMMENT '解读状态',
  `input_tokens` int DEFAULT NULL,
  `output_tokens` int DEFAULT NULL,
  `created_at` datetime DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=22 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
;
